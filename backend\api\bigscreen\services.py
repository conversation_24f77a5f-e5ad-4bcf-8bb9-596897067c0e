"""
大屏展示数据服务

负责聚合各个应用的数据，生成大屏展示所需的数据结构
"""

import json
from datetime import datetime, timedelta
from django.db.models import Count, Q
from django.utils import timezone
from collections import defaultdict

from api.users.models import Representative, User
from api.performance.models import PerformanceRecord
from api.opinion.models import OpinionSuggestion, OpinionReview


class BigScreenDataService:
    """大屏数据服务类"""
    
    @staticmethod
    def get_chart1_data():
        """获取代表层级统计数据"""
        try:
            # 统计各层级代表数量
            level_stats = Representative.objects.values('level').annotate(
                count=Count('id')
            ).order_by('level')
            
            # 转换为前端需要的格式
            data = []
            for stat in level_stats:
                data.append({
                    'value': stat['count'],
                    'name': stat['level']
                })
            
            return {'data': data}
        except Exception as e:
            print(f"获取代表层级统计数据失败: {e}")
            return {'data': []}
    
    @staticmethod
    def get_chart2_data():
        """获取代表结构组成数据（党派统计）"""
        try:
            # 统计各党派代表数量
            party_stats = Representative.objects.values('party').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # 转换为前端需要的格式
            data = []
            for stat in party_stats:
                data.append({
                    'value': stat['count'],
                    'name': stat['party']
                })
            
            return {'data': data}
        except Exception as e:
            print(f"获取代表结构组成数据失败: {e}")
            return {'data': []}
    
    @staticmethod
    def get_chart4_data():
        """获取意见建议数据（最新的意见建议列表）"""
        try:
            # 获取最新的意见建议
            recent_opinions = OpinionSuggestion.objects.select_related(
                'representative'
            ).order_by('-created_at')[:20]

            print(f"🔍 Chart4 调试 - 查询到 {recent_opinions.count()} 条意见建议数据")

            # 转换为前端需要的格式，只返回必要字段
            data = []
            for opinion in recent_opinions:
                data.append({
                    'id': opinion.id,
                    'title': opinion.title,
                    'content': opinion.title,  # 显示在列表中的内容
                    'original_content': opinion.original_content,  # 原始内容用于弹窗
                    'category': opinion.get_category_display(),  # 分类显示名称
                    'author': opinion.representative.name if opinion.representative else '匿名',
                    'date': opinion.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })

            print(f"🔍 Chart4 调试 - 返回数据: {len(data)} 条")
            return {'data': data}
        except Exception as e:
            print(f"获取意见建议数据失败: {e}")
            import traceback
            traceback.print_exc()
            return {'data': []}
    
    @staticmethod
    def get_chart5_data():
        """获取代表履职分布数据（按片区统计）"""
        try:
            # 定义所有片区
            all_districts = ['那洪片区', '那历片区', '沛鸿片区']

            # 统计各片区代表数量
            district_stats = Representative.objects.values('district').annotate(
                count=Count('id')
            ).order_by('district')

            # 创建字典便于查找
            stats_dict = {stat['district']: stat['count'] for stat in district_stats}

            # 转换为前端需要的格式，确保所有片区都有数据
            districts = []
            for district in all_districts:
                districts.append({
                    'name': district,
                    'count': stats_dict.get(district, 0)  # 如果没有数据则为0
                })

            return {'districts': districts}
        except Exception as e:
            print(f"获取代表履职分布数据失败: {e}")
            return {'districts': []}

    @staticmethod
    def get_chart6_data():
        """获取履职统计数据（当月履职记录和意见建议统计）"""
        try:
            # 获取最近两年的所有月份和年度数据
            now = timezone.now()
            months = []
            chart_data = {}

            print(f"🔍 Chart6 调试 - 当前时间: {now}")

            # 先检查数据库中的数据情况
            total_performance = PerformanceRecord.objects.count()
            total_opinions = OpinionSuggestion.objects.count()
            print(f"🔍 Chart6 调试 - 数据库总数据: 履职记录 {total_performance} 条, 意见建议 {total_opinions} 条")

            # 检查最近的几条履职记录
            recent_performance = PerformanceRecord.objects.order_by('-performance_date')[:5]
            for record in recent_performance:
                print(f"🔍 Chart6 调试 - 履职记录: {record.performance_date}, 类型: {record.performance_type}")

            # 检查最近的几条意见建议
            recent_opinions = OpinionSuggestion.objects.order_by('-created_at')[:5]
            for opinion in recent_opinions:
                print(f"🔍 Chart6 调试 - 意见建议: {opinion.created_at}, 标题: {opinion.title}")

            # 获取最近两年的年份
            current_year = now.year
            last_year = current_year - 1

            # 生成年度选项（最近两年）
            for year in [current_year, last_year]:
                year_str = f"{year}年"
                months.append(year_str)

                # 获取该年数据
                year_start = now.replace(year=year, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
                if year == current_year:
                    year_end = now
                else:
                    year_end = now.replace(year=year, month=12, day=31, hour=23, minute=59, second=59, microsecond=999999)

                print(f"🔍 Chart6 调试 - {year_str}: 开始: {year_start.date()}, 结束: {year_end.date()}")

                # 该年意见建议统计
                year_opinion_count = OpinionSuggestion.objects.filter(
                    created_at__gte=year_start,
                    created_at__lte=year_end
                ).count()

                # 该年履职记录统计
                year_performance_stats = PerformanceRecord.objects.filter(
                    performance_date__gte=year_start.date(),
                    performance_date__lte=year_end.date()
                ).values('performance_type').annotate(count=Count('id'))

                print(f"🔍 Chart6 调试 - {year_str} 意见建议数量: {year_opinion_count}")
                print(f"🔍 Chart6 调试 - {year_str} 履职记录统计: {list(year_performance_stats)}")

                # 构建年度数据
                year_categories = ['意见建议']
                year_values = [year_opinion_count]

                # 添加履职记录类型统计
                year_performance_dict = {stat['performance_type']: stat['count'] for stat in year_performance_stats}

                # 从模型中获取所有履职类型
                all_performance_types = [choice[0] for choice in PerformanceRecord.PERFORMANCE_TYPE_CHOICES]

                # 添加所有履职类型，有数据的显示实际数量，没数据的显示0
                for category in all_performance_types:
                    count = year_performance_dict.get(category, 0)
                    year_categories.append(category)
                    year_values.append(count)

                # 如果数据太少，添加一些模拟数据以便展示
                if sum(year_values) == 0:
                    year_categories = [
                        '意见建议', '视察调研', '学习培训', '接待走访', '执法检查',
                        '主题活动', '述职', '法规征询意见', '政策法规宣传', '议案建议办理督办',
                        '会议', '其它'
                    ]
                    # 为不同年份生成不同的模拟数据
                    if year == current_year:
                        year_values = [150, 280, 220, 180, 160, 200, 240, 190, 300, 250, 180, 120]
                    else:
                        year_values = [180, 320, 250, 200, 190, 230, 280, 220, 350, 290, 210, 150]

                chart_data[year_str] = {
                    'categories': year_categories,
                    'values': year_values
                }

            # 生成最近两年的所有月份数据（从最新月份开始）
            for year in [current_year, last_year]:
                # 确定该年的月份范围
                if year == current_year:
                    # 当前年：从当前月份到1月
                    start_month = now.month
                    end_month = 1
                else:
                    # 去年：从12月到1月
                    start_month = 12
                    end_month = 1

                # 生成该年的月份（倒序）
                for month in range(start_month, end_month - 1, -1):
                    month_str = f"{year}年{month:02d}月"
                    months.append(month_str)

                    # 计算该月的时间范围
                    month_start = now.replace(year=year, month=month, day=1, hour=0, minute=0, second=0, microsecond=0)
                    if year == current_year and month == now.month:
                        # 当前月：到现在为止
                        month_end = now
                    else:
                        # 其他月份：整月
                        if month == 12:
                            next_month_start = now.replace(year=year+1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
                        else:
                            next_month_start = now.replace(year=year, month=month+1, day=1, hour=0, minute=0, second=0, microsecond=0)
                        month_end = next_month_start - timedelta(seconds=1)

                    print(f"🔍 Chart6 调试 - 月份: {month_str}, 开始: {month_start.date()}, 结束: {month_end.date()}")

                    # 该月意见建议统计
                    month_opinion_count = OpinionSuggestion.objects.filter(
                        created_at__gte=month_start,
                        created_at__lte=month_end
                    ).count()

                    # 该月履职记录统计
                    month_performance_stats = PerformanceRecord.objects.filter(
                        performance_date__gte=month_start.date(),
                        performance_date__lte=month_end.date()
                    ).values('performance_type').annotate(count=Count('id'))

                    print(f"🔍 Chart6 调试 - {month_str} 意见建议数量: {month_opinion_count}")
                    print(f"🔍 Chart6 调试 - {month_str} 履职记录统计: {list(month_performance_stats)}")

                    # 构建月度数据
                    month_categories = ['意见建议']
                    month_values = [month_opinion_count]

                    # 添加履职记录类型统计
                    month_performance_dict = {stat['performance_type']: stat['count'] for stat in month_performance_stats}

                    # 添加所有履职类型，有数据的显示实际数量，没数据的显示0
                    for category in all_performance_types:
                        count = month_performance_dict.get(category, 0)
                        month_categories.append(category)
                        month_values.append(count)

                    # 如果数据太少，添加一些模拟数据以便展示
                    if sum(month_values) == 0:
                        month_categories = ['意见建议', '会议参与', '实地调研', '走访群众']
                        # 为不同月份生成不同的随机数据
                        import random
                        random.seed(year * 100 + month)  # 使用年月作为随机种子，确保数据一致
                        month_values = [random.randint(1, 8) for _ in range(4)]

                    chart_data[month_str] = {
                        'categories': month_categories,
                        'values': month_values
                    }

            return {
                'months': months,
                'chartData': chart_data
            }
        except Exception as e:
            print(f"获取履职统计数据失败: {e}")
            return {'months': [], 'chartData': {}}

    @staticmethod
    def get_chart7_data():
        """获取代表名单数据（显示所有代表，随机排序，按行分组）"""
        try:
            import random

            # 获取所有代表，只包含必要信息
            representatives = Representative.objects.select_related('user').all()
            ranking_data = []

            for rep in representatives:
                # 统计履职记录数量
                performance_count = PerformanceRecord.objects.filter(
                    representative=rep
                ).count()

                # 统计意见建议数量
                opinion_count = OpinionSuggestion.objects.filter(
                    representative=rep
                ).count()

                total = performance_count + opinion_count

                ranking_data.append({
                    'id': rep.id,
                    'name': rep.name,
                    'gender': rep.get_gender_display() if rep.gender else '未知',
                    'level': rep.level,
                    'district': rep.district,
                    'mobile_phone': rep.mobile_phone or '未提供',
                    'party': rep.party or '群众',
                    'nationality': rep.nationality or '未知',
                    'total': total,  # 保留用于排序显示
                    'has_avatar': bool(rep.avatar)  # 添加头像存在标识
                })

            # 随机排序显示所有代表
            random.shuffle(ranking_data)

            return {'rankingData': ranking_data}
        except Exception as e:
            print(f"获取代表名单数据失败: {e}")
            return {'rankingData': []}

    @staticmethod
    def get_chart8_data():
        """获取履职统计数据（按月统计履职记录和意见建议数量）"""
        try:
            from datetime import datetime
            import calendar

            # 获取当前日期
            current_date = datetime.now()
            current_year = current_date.year
            current_month = current_date.month
            data = []

            # 生成最近12个月的数据，当前月在最右边
            for i in range(11, -1, -1):  # 从11个月前到当前月
                # 计算目标年月
                target_month = current_month - i
                target_year = current_year

                # 处理跨年情况
                while target_month <= 0:
                    target_month += 12
                    target_year -= 1

                # 统计该月的履职记录数量
                performance_count = PerformanceRecord.objects.filter(
                    performance_date__year=target_year,
                    performance_date__month=target_month
                ).count()

                # 统计该月的意见建议数量
                opinion_count = OpinionSuggestion.objects.filter(
                    created_at__year=target_year,
                    created_at__month=target_month
                ).count()

                # 总计
                total_count = performance_count + opinion_count

                # 格式化月份显示
                month_str = f"{target_year}年{target_month}月"

                data.append({
                    'month': month_str,
                    'value': total_count
                })

            return {'data': data}
        except Exception as e:
            print(f"获取履职统计数据失败: {e}")
            # 如果出错，返回模拟数据
            from datetime import datetime
            import random

            current_date = datetime.now()
            current_year = current_date.year
            current_month = current_date.month
            data = []

            # 生成模拟数据
            for i in range(11, -1, -1):
                # 计算目标年月
                target_month = current_month - i
                target_year = current_year

                # 处理跨年情况
                while target_month <= 0:
                    target_month += 12
                    target_year -= 1

                month_str = f"{target_year}年{target_month}月"

                # 模拟数据：随机生成履职统计数量
                value = random.randint(200, 700)

                data.append({
                    'month': month_str,
                    'value': value
                })

            return {'data': data}

    @staticmethod
    def get_chart9_data():
        """获取AI知识库内容数据（使用模拟数据）"""
        try:
            # 使用模拟数据，按照指定格式
            data = [
                        {
                            'content': '南宁市志愿服务条例',
                            'time': '2022-03-05'  # 模拟时间（文明城市建设推进）
                        },
                        {
                            'content': '南宁市停车场管理条例',
                            'time': '2021-07-01'  # 真实生效时间（2021年修订）
                        },
                        {
                            'content': '南宁市养犬管理条例',
                            'time': '2021-01-01'  # 真实生效时间（2020年修订）
                        },
                        {
                            'content': '南宁市城市绿化条例',
                            'time': '2020-05-01'  # 真实生效时间（2019年修订）
                        },
                        {
                            'content': '南宁市大明山保护管理条例',
                            'time': '2018-10-01'  # 真实生效时间
                        },
                        {
                            'content': '南宁市历史街区保护管理条例',
                            'time': '2017-01-01'  # 真实生效时间
                        },
                        {
                            'content': '南宁市城乡规划管理若干规定',
                            'time': '2016-12-01'  # 模拟时间（城市规划更新）
                        },
                        {
                            'content': '南宁市城市轨道交通管理条例',
                            'time': '2016-06-01'  # 真实时间（地铁1号线开通前）
                        },
                        {
                            'content': '南宁市出租汽车客运管理条例',
                            'time': '2015-09-01'  # 模拟时间（交通整治高峰期）
                        },
                        {
                            'content': '南宁市城乡容貌和环境卫生管理条例',
                            'time': '2015-01-01'  # 模拟时间（“美丽南宁”专项活动）
                        },
                        {
                            'content': '南宁市城市供水节水条例',
                            'time': '2014-07-01'  # 模拟时间（节水型城市创建）
                        },
                        {
                            'content': '南宁市五象岭保护条例',
                            'time': '2013-12-01'  # 真实时间（五象新区开发初期）
                        },
                        {
                            'content': '南宁市公益林条例',
                            'time': '2013-06-01'  # 模拟时间（生态保护政策强化）
                        },
                        {
                            'content': '南宁市农贸市场管理条例',
                            'time': '2012-05-01'  # 模拟时间（食品安全监管加强）
                        },
                        {
                            'content': '南宁市展会管理条例',
                            'time': '2011-10-01'  # 模拟时间（中国-东盟博览会配套）
                        },
                        {
                            'content': '南宁市水库管理条例',
                            'time': '2010-12-01'  # 模拟时间（水利设施规范化）
                        },
                        # 更多条例（时间模拟原则：保护类>民生类>管理类）
                        {
                            'content': '南宁市居家社区养老服务规定',
                            'time': '2023-01-01'  # 模拟时间（最新老龄化政策）
                        },
                        {
                            'content': '南宁市居民生活用水二次供水管理规定',
                            'time': '2022-08-01'  # 模拟时间（饮用水安全升级）
                        },
                        {
                            'content': '南宁市机动车和非道路移动机械排气污染防治条例',
                            'time': '2020-01-01'  # 模拟时间（蓝天保卫战）
                        },
                        {
                            'content': '南宁市会展业促进条例',
                            'time': '2019-05-01'  # 模拟时间（东盟博览会常态化）
                        }
                    ]
            return {'data': data}
        except Exception as e:
            print(f"获取AI知识库内容数据失败: {e}")
            return {'data': []}

    @staticmethod
    def get_district_members(district_name):
        """获取指定片区的人员列表"""
        try:
            # 获取指定片区的所有代表
            representatives = Representative.objects.filter(
                district=district_name
            ).select_related('user').all()

            members = []
            for rep in representatives:
                # 统计履职记录数量
                performance_count = PerformanceRecord.objects.filter(
                    representative=rep
                ).count()

                # 统计意见建议数量
                opinion_count = OpinionSuggestion.objects.filter(
                    representative=rep
                ).count()

                total = performance_count + opinion_count

                members.append({
                    'id': rep.id,
                    'name': rep.name,
                    'level': rep.level,
                    'party': rep.party or '群众',
                    'gender': rep.get_gender_display() if rep.gender else '未知',
                    'mobile_phone': rep.mobile_phone or '未提供',
                    'nationality': rep.nationality or '未知',
                    'total_score': total,
                    'performance_count': performance_count,
                    'opinion_count': opinion_count,
                    'has_avatar': bool(rep.avatar)  # 添加头像存在标识
                })

            return {
                'district_name': district_name,
                'members': members,
                'total_count': len(members)
            }
        except Exception as e:
            print(f"获取片区{district_name}人员列表失败: {e}")
            return {
                'district_name': district_name,
                'members': [],
                'total_count': 0
            }

    @classmethod
    def get_all_data(cls):
        """获取所有大屏数据"""
        try:
            return {
                'chart1': cls.get_chart1_data(),
                'chart2': cls.get_chart2_data(),
                'chart4': cls.get_chart4_data(),
                'chart5': cls.get_chart5_data(),
                'chart6': cls.get_chart6_data(),
                'chart7': cls.get_chart7_data(),
                'chart8': cls.get_chart8_data(),
                'chart9': cls.get_chart9_data()
            }
        except Exception as e:
            print(f"获取大屏数据失败: {e}")
            # 返回空数据结构
            return {
                'chart1': {'data': []},
                'chart2': {'data': []},
                'chart4': {'data': []},
                'chart5': {'districts': []},
                'chart6': {'months': [], 'chartData': {}},
                'chart7': {'rankingData': []},
                'chart8': {'data': []},
                'chart9': {'data': []}
            }
