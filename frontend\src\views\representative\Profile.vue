<template>
  <div class="profile-container">
    <div class="page-header">
      <h2>个人信息管理</h2>
      <p>管理您的基本信息和联系方式</p>
    </div>

    <el-card class="profile-card">
      <template #header>
        <span class="card-title">基本信息</span>
      </template>
      
      <el-form
        :model="profileForm"
        :rules="profileRules"
        label-width="120px"
        size="large"
      >
        <!-- 头像上传 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="头像" prop="avatar">
              <div class="avatar-upload-container">
                <div class="avatar-preview">
                  <img
                    v-if="profileForm.avatar"
                    :src="profileForm.avatar"
                    alt="头像预览"
                    class="avatar-image"
                  />
                  <div v-else class="avatar-placeholder">
                    <el-icon size="40"><Avatar /></el-icon>
                    <span>暂无头像</span>
                  </div>
                </div>
                <div class="avatar-upload-actions" v-if="isEditing">
                  <el-upload
                    ref="avatarUpload"
                    :show-file-list="false"
                    :before-upload="beforeAvatarUpload"
                    :on-change="handleAvatarChange"
                    :auto-upload="false"
                    accept="image/*"
                  >
                    <el-button type="primary" size="small">
                      <el-icon><Upload /></el-icon>
                      选择头像
                    </el-button>
                  </el-upload>
                  <el-button
                    v-if="profileForm.avatar"
                    type="danger"
                    size="small"
                    @click="removeAvatar"
                  >
                    <el-icon><Delete /></el-icon>
                    删除头像
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第一行：代表层级、姓名 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="代表层级" prop="level">
              <el-select
                v-model="profileForm.level"
                :disabled="!isEditing"
                placeholder="请选择代表层级"
                style="width: 100%"
              >
                <el-option
                  v-for="level in REPRESENTATIVE_LEVELS"
                  :key="level.value"
                  :label="level.label"
                  :value="level.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model="profileForm.name"
                :disabled="!isEditing"
                placeholder="请输入姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：性别、民族 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select
                v-model="profileForm.gender"
                :disabled="!isEditing"
                placeholder="请选择性别"
                style="width: 100%"
              >
                <el-option
                  v-for="gender in GENDER_OPTIONS"
                  :key="gender.value"
                  :label="gender.label"
                  :value="gender.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="民族" prop="nationality">
              <el-select
                v-model="profileForm.nationality"
                :disabled="!isEditing"
                placeholder="选择民族（可选）"
                style="width: 100%"
                filterable
              >
                <el-option v-for="nation in NATIONALITY_OPTIONS" :key="nation" :label="nation" :value="nation" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：所属片区 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属片区" prop="district">
              <el-select
                v-model="profileForm.district"
                :disabled="!isEditing"
                placeholder="选择所属片区（可选）"
                style="width: 100%"
              >
                <el-option
                  v-for="district in DISTRICT_OPTIONS"
                  :key="district.value"
                  :label="district.label"
                  :value="district.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：出生日期、籍贯 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birth_date">
              <el-date-picker
                v-model="profileForm.birth_date"
                type="month"
                :disabled="!isEditing"
                placeholder="请选择出生年月"
                style="width: 100%"
                format="YYYY年MM月"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="籍贯" prop="birthplace">
              <el-input 
                v-model="profileForm.birthplace" 
                :disabled="!isEditing"
                placeholder="请输入籍贯"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：党派、现任职务 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="党派" prop="party">
              <el-select
                v-model="profileForm.party"
                :disabled="!isEditing"
                placeholder="请选择党派"
                style="width: 100%"
              >
                <el-option v-for="party in PARTY_OPTIONS" :key="party" :label="party" :value="party" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="现任职务" prop="current_position">
              <el-input 
                v-model="profileForm.current_position" 
                :disabled="!isEditing"
                placeholder="请输入现任职务"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第五行：移动电话号码、学历 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="移动电话号码" prop="mobile_phone">
              <el-input 
                v-model="profileForm.mobile_phone" 
                :disabled="!isEditing"
                placeholder="请输入移动电话号码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历" prop="education">
              <el-select
                v-model="profileForm.education"
                :disabled="!isEditing"
                placeholder="选择学历（可选）"
                style="width: 100%"
              >
                <el-option v-for="edu in EDUCATION_OPTIONS" :key="edu" :label="edu" :value="edu" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第六行：毕业院校、所学专业 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="毕业院校" prop="graduated_school">
              <el-input 
                v-model="profileForm.graduated_school" 
                :disabled="!isEditing"
                placeholder="请输入毕业院校（可选）"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所学专业" prop="major">
              <el-input 
                v-model="profileForm.major" 
                :disabled="!isEditing"
                placeholder="请输入所学专业（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button 
            v-if="!isEditing" 
            type="primary" 
            @click="startEdit"
          >
            编辑信息
          </el-button>
          <div v-else>
            <el-button type="primary" @click="saveProfile">
              保存修改
            </el-button>
            <el-button @click="cancelEdit">
              取消
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 履职统计 -->
    <!-- <el-card class="stats-card">
      <template #header>
        <span class="card-title">履职统计</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.totalRecords }}</div>
            <div class="stat-label">履职记录总数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.opinionsSubmitted }}</div>
            <div class="stat-label">提交意见数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.opinionsResolved }}</div>
            <div class="stat-label">已办结意见</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.currentYearRecords }}</div>
            <div class="stat-label">本年履职次数</div>
          </div>
        </el-col>
      </el-row>
    </el-card> -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Avatar, Upload, Delete } from '@element-plus/icons-vue'
import {
  REPRESENTATIVE_LEVELS,
  DISTRICT_OPTIONS,
  GENDER_OPTIONS,
  NATIONALITY_OPTIONS,
  PARTY_OPTIONS,
  EDUCATION_OPTIONS
} from '@/constants/representative'

const userStore = useUserStore()

// 编辑状态
const isEditing = ref(false)

// 表单数据 - 对应后端Representative模型字段
const profileForm = reactive({
  level: '', // 代表层级（只读）
  name: '', // 姓名（只读）
  gender: '', // 性别
  nationality: '', // 民族
  district: '', // 所属片区（必填）
  birth_date: '', // 出生日期
  birthplace: '', // 籍贯
  party: '', // 党派
  current_position: '', // 现任职务
  mobile_phone: '', // 移动电话号码
  education: '', // 学历
  graduated_school: '', // 毕业院校（可选）
  major: '', // 所学专业（可选）
  avatar: '' // 头像（base64格式）
})

// 原始数据备份
let originalData = {}

// 统计数据
const stats = ref({
  totalRecords: 12,
  opinionsSubmitted: 8,
  opinionsResolved: 6,
  currentYearRecords: 5
})

// 选项数据已移至统一配置文件 @/constants/representative

// 表单验证规则
const profileRules = {
  // 必填字段：代表层级、姓名、性别、籍贯、出生日期、党派、现任职务、电话
  level: [
    { required: true, message: '请选择代表层级', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  birth_date: [
    { required: true, message: '请选择出生年月', trigger: 'change' }
  ],
  birthplace: [
    { required: true, message: '请输入籍贯', trigger: 'blur' }
  ],
  party: [
    { required: true, message: '请选择党派', trigger: 'change' }
  ],
  current_position: [
    { required: true, message: '请输入现任职务', trigger: 'blur' }
  ],
  mobile_phone: [
    { required: true, message: '请输入移动电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  // 非必填字段：民族、所属片区、学历、毕业院校、所学专业
  nationality: [
    { required: false }
  ],
  district: [
    { required: false }
  ],
  education: [
    { required: false }
  ]
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  // 备份原始数据
  originalData = { ...profileForm }
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  // 恢复原始数据
  Object.assign(profileForm, originalData)
}

// 保存修改
const saveProfile = async () => {
  // 验证必填项：代表层级、姓名、性别、籍贯、出生日期、党派、现任职务、电话
  if (!profileForm.level) {
    ElMessage.error('请选择代表层级')
    return
  }
  if (!profileForm.name || profileForm.name.trim() === '') {
    ElMessage.error('请输入姓名')
    return
  }
  if (profileForm.name.trim().length < 2 || profileForm.name.trim().length > 20) {
    ElMessage.error('姓名长度应在2-20个字符之间')
    return
  }
  if (!profileForm.gender) {
    ElMessage.error('请选择性别')
    return
  }
  if (!profileForm.birth_date) {
    ElMessage.error('请选择出生年月')
    return
  }
  if (!profileForm.birthplace) {
    ElMessage.error('请输入籍贯')
    return
  }
  if (!profileForm.party) {
    ElMessage.error('请选择党派')
    return
  }
  if (!profileForm.current_position) {
    ElMessage.error('请输入现任职务')
    return
  }
  if (!profileForm.mobile_phone) {
    ElMessage.error('请输入移动电话号码')
    return
  }
  if (!/^1[3-9]\d{9}$/.test(profileForm.mobile_phone)) {
    ElMessage.error('请输入正确的手机号码')
    return
  }
  // 非必填字段：民族、所属片区、学历、毕业院校、所学专业 - 不需要验证

  try {
    // 准备更新数据（只包含可编辑字段）
    // 处理出生日期格式（月份精确度，补充为该月第一天）
    let birthDate = profileForm.birth_date
    if (birthDate && birthDate.match(/^\d{4}-\d{2}$/)) {
      birthDate = birthDate + '-01'
    }

    const updateData = {
      level: profileForm.level,
      name: profileForm.name.trim(),
      gender: profileForm.gender,
      nationality: profileForm.nationality,
      district: profileForm.district,
      birth_date: birthDate,
      birthplace: profileForm.birthplace,
      party: profileForm.party,
      current_position: profileForm.current_position,
      mobile_phone: profileForm.mobile_phone,
      education: profileForm.education,
      graduated_school: profileForm.graduated_school || '',
      major: profileForm.major || '',
      avatar: profileForm.avatar || ''
    }

    // 调用后端API更新用户信息
    const result = await userStore.updateProfile(updateData)
    
    if (result.success) {
      isEditing.value = false
      ElMessage.success('信息更新成功')
      
      // 重新获取用户信息以同步最新数据
      await userStore.fetchUserInfo()
      loadUserData()
    } else {
      ElMessage.error(result.message || '更新失败，请重试')
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

// 性别显示映射
const getGenderDisplay = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女'
  }
  return genderMap[gender] || gender
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理头像文件变化
const handleAvatarChange = (file) => {
  if (!beforeAvatarUpload(file.raw)) {
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    profileForm.avatar = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 删除头像
const removeAvatar = () => {
  profileForm.avatar = ''
}

// 加载用户数据
const loadUserData = () => {
  const userInfo = userStore.userInfo
  const repInfo = userInfo.representative_info || {}

  // 处理出生日期格式，从YYYY-MM-DD转换为YYYY-MM
  let birthDate = repInfo.birth_date || ''
  if (birthDate && birthDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
    birthDate = birthDate.substring(0, 7) // 取前7位，即YYYY-MM
  }

  Object.assign(profileForm, {
    level: repInfo.level || '',
    name: repInfo.name || userStore.userName,
    gender: repInfo.gender || '',
    nationality: repInfo.nationality || '',
    district: repInfo.district || '',
    birth_date: birthDate,
    birthplace: repInfo.birthplace || '',
    party: repInfo.party || '',
    current_position: repInfo.current_position || '',
    mobile_phone: repInfo.mobile_phone || '',
    education: repInfo.education || '',
    graduated_school: repInfo.graduated_school || '',
    major: repInfo.major || '',
    avatar: repInfo.avatar || ''
  })
}

// 初始化数据
onMounted(async () => {
  try {
    // 确保获取最新用户信息
    await userStore.fetchUserInfo()
    loadUserData()
  } catch (error) {
    console.warn('获取用户信息失败:', error)
    loadUserData() // 即使失败也尝试加载现有数据
  }
})
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: var(--china-red);
  margin-bottom: 8px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.profile-card,
.stats-card {
  margin-bottom: 20px;
}

.card-title {
  font-weight: bold;
  color: var(--china-red);
  font-size: 16px;
}

.stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--china-red);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

/* 禁用状态的输入框样式 */
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  box-shadow: 0 0 0 1px #e4e7ed inset;
}

/* 头像上传样式 */
.avatar-upload-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #dcdfe6;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}

.avatar-placeholder .el-icon {
  margin-bottom: 5px;
}

.avatar-upload-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.avatar-upload-actions .el-button {
  width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }
  
  .stat-item {
    padding: 15px 0;
  }
  
  .stat-value {
    font-size: 24px;
  }
}
</style> 