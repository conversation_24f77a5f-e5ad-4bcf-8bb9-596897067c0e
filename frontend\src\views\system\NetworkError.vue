<template>
  <div class="network-error-container">
    <el-result
      icon="warning"
      title="网络连接失败"
      sub-title="后端服务暂时不可用，请稍后重试或联系管理员"
    >
      <template #extra>
        <el-button type="primary" @click="goHome" :loading="goingHome">
          {{ goingHome ? '跳转中...' : '返回首页' }}
        </el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 按钮状态
const goingHome = ref(false)

/**
 * 返回首页 - 直接跳转，不检测网络
 */
const goHome = () => {
  goingHome.value = true
  
  try {
    // 直接跳转到首页，不进行网络检测
    const homePath = getDefaultHomePage()
    router.push(homePath)
  } catch (error) {
    console.error('跳转失败:', error)
  } finally {
    // 延迟重置状态，避免闪烁
    setTimeout(() => {
      goingHome.value = false
    }, 500)
  }
}

/**
 * 获取默认首页路径
 */
const getDefaultHomePage = () => {
  // 如果用户已登录，根据角色跳转
  if (userStore.isLoggedIn && userStore.userInfo?.role) {
    return userStore.userInfo.role === 'representative' ? '/representative' : '/staff'
  }
  
  // 未登录用户跳转到登录页
  return '/login'
}
</script>

<style scoped>
.network-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style> 
