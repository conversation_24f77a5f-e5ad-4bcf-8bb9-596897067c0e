<template>
  <div class="account-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>账号管理</h2>
      <p>管理系统中所有用户账号，包括人大代表和站点工作人员</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增账号
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          v-if="searchKeyword || roleFilter || statusFilter"
          @click="clearFilters"
          type="info"
          plain
        >
          清空筛选
        </el-button>
      </div>

      <div class="toolbar-right">
        <el-dropdown @command="handleExportCommand" split-button type="success" @click="() => exportUsers()">
          <el-icon><Download /></el-icon>
          导出账号
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="all">导出全部</el-dropdown-item>
              <el-dropdown-item command="representative">仅导出代表</el-dropdown-item>
              <el-dropdown-item command="staff">仅导出工作人员</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown @command="handleImportCommand" split-button type="warning" @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入账号
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="representative">导入代表账号</el-dropdown-item>
              <el-dropdown-item command="staff">导入工作人员账号</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-select v-model="roleFilter" placeholder="筛选角色" style="width: 150px;" @change="handleSearch">
        <el-option label="全部角色" value="" />
        <el-option label="人大代表" value="representative" />
        <el-option label="工作人员" value="staff" />
      </el-select>

      <el-select v-model="statusFilter" placeholder="筛选状态" style="width: 150px;" @change="handleSearch">
        <el-option label="全部状态" value="" />
        <el-option label="启用" value="true" />
        <el-option label="禁用" value="false" />
      </el-select>

      <el-input
        v-model="searchKeyword"
        placeholder="搜索用户名或姓名"
        clearable
        @input="handleSearchInput"
        @keyup.enter="handleSearch"
        style="width: 300px;"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append>
          <el-button @click="handleSearch" :icon="Search" />
        </template>
      </el-input>
    </div>

    <!-- 账号列表 -->
    <el-card class="account-list-card">
      <el-table
        :data="userList"
        v-loading="loading"
        stripe
        style="width: 100%"
        empty-text="暂无用户数据"
        :default-sort="{ prop: 'created_at', order: 'descending' }"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column label="真实姓名" width="120">
          <template #default="scope">
            {{ getUserDisplayName(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="role" label="角色" width="120">
          <template #default="scope">
            <el-tag 
              :type="scope.row.role === 'representative' ? 'success' : 'primary'"
              class="role-tag"
              size="small"
            >
              {{ scope.row.role_display }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="所属部门/层级" width="180">
          <template #default="scope">
            {{ getUserDepartment(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="联系电话" width="130">
          <template #default="scope">
            {{ getUserPhone(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.is_active ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login_at" label="最后登录" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.last_login_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="360" fixed="right">
          <template #default="scope">
            <div class="table-actions">
              <el-button size="small" @click="viewDetails(scope.row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button size="small" type="warning" @click="resetPassword(scope.row)">
                <el-icon><Key /></el-icon>
                重置密码
              </el-button>
              <el-tooltip 
                :content="scope.row.id === currentUserId && scope.row.is_active ? '不能禁用自己的账号' : ''"
                :disabled="!(scope.row.id === currentUserId && scope.row.is_active)"
                placement="top"
              >
                <el-button 
                  size="small" 
                  :type="scope.row.is_active ? 'danger' : 'success'"
                  @click="toggleAccountStatus(scope.row)"
                  :disabled="scope.row.id === currentUserId && scope.row.is_active"
                >
                  <el-icon><Switch /></el-icon>
                  {{ scope.row.is_active ? '禁用' : '启用' }}
                </el-button>
              </el-tooltip>
              <el-tooltip 
                :content="scope.row.id === currentUserId ? '不能删除自己的账号' : ''"
                :disabled="scope.row.id !== currentUserId"
                placement="top"
              >
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteUser(scope.row)"
                  :disabled="scope.row.id === currentUserId"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="pagination-total">
            共 {{ totalCount }} 条记录，当前第 {{ currentPage }} / {{ Math.ceil(totalCount / pageSize) || 1 }} 页
          </span>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalCount"
          :pager-count="7"
          :disabled="loading"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          small
        />
      </div>
    </el-card>

    <!-- 新增账号对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增账号"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="accountFormRef"
        :model="accountForm"
        :rules="formRules"
        label-width="120px"
      >
        <!-- 基础信息 -->
        <h4 style="margin: 0 0 15px 0; color: var(--el-text-color-primary);">基础信息</h4>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input 
                v-model="accountForm.username" 
                placeholder="请输入用户名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色" prop="role">
              <el-select v-model="accountForm.role" placeholder="请选择角色" style="width: 100%" @change="handleRoleChange">
                <el-option label="人大代表" value="representative" />
                <el-option label="站点工作人员" value="staff" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="初始密码" prop="password">
              <el-input 
                v-model="accountForm.password" 
                type="password" 
                placeholder="请输入初始密码"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirm_password">
              <el-input 
                v-model="accountForm.confirm_password" 
                type="password" 
                placeholder="请再次输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="账号状态" prop="is_active">
          <el-radio-group v-model="accountForm.is_active">
            <el-radio :value="true">启用</el-radio>
            <el-radio :value="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 人大代表信息 -->
        <div v-if="accountForm.role === 'representative'">
          <h4 style="margin: 20px 0 15px 0; color: var(--el-text-color-primary);">代表信息</h4>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="representative_name">
                <el-input v-model="accountForm.representative_name" placeholder="请输入代表姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="代表层级" prop="representative_levels">
                <el-select
                  v-model="accountForm.representative_levels"
                  placeholder="请选择层级（可多选）"
                  style="width: 100%"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="2"
                >
                  <el-option
                    v-for="level in REPRESENTATIVE_LEVELS"
                    :key="level.value"
                    :label="level.label"
                    :value="level.value"
                  />
                </el-select>
                <div class="form-tip">
                  <el-icon><InfoFilled /></el-icon>
                  <span>可以选择多个代表层级</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="性别" prop="representative_gender">
                <el-radio-group v-model="accountForm.representative_gender">
                  <el-radio
                    v-for="gender in GENDER_OPTIONS"
                    :key="gender.value"
                    :value="gender.value"
                  >
                    {{ gender.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="民族" prop="representative_nationality">
                <el-select v-model="accountForm.representative_nationality" placeholder="选择民族（可选）" style="width: 100%" filterable>
                  <el-option
                    v-for="nation in NATIONALITY_OPTIONS"
                    :key="nation"
                    :label="nation"
                    :value="nation"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属片区" prop="representative_district">
                <el-select v-model="accountForm.representative_district" placeholder="选择所属片区（可选）" style="width: 100%">
                  <el-option
                    v-for="district in DISTRICT_OPTIONS"
                    :key="district.value"
                    :label="district.label"
                    :value="district.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="出生日期" prop="representative_birth_date">
                <el-date-picker
                  v-model="accountForm.representative_birth_date"
                  type="month"
                  placeholder="请选择出生年月"
                  style="width: 100%"
                  format="YYYY年MM月"
                  value-format="YYYY-MM"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="籍贯" prop="representative_birthplace">
                <el-input v-model="accountForm.representative_birthplace" placeholder="请输入籍贯" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="党派" prop="representative_party">
                <el-select v-model="accountForm.representative_party" placeholder="请选择党派" style="width: 100%">
                  <el-option
                    v-for="party in PARTY_OPTIONS"
                    :key="party"
                    :label="party"
                    :value="party"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="现任职务" prop="representative_current_position">
                <el-input v-model="accountForm.representative_current_position" placeholder="请输入现任职务" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="手机号码" prop="representative_mobile_phone">
                <el-input v-model="accountForm.representative_mobile_phone" placeholder="请输入手机号码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学历" prop="representative_education">
                <el-select v-model="accountForm.representative_education" placeholder="选择学历（可选）" style="width: 100%">
                  <el-option
                    v-for="edu in EDUCATION_OPTIONS"
                    :key="edu"
                    :label="edu"
                    :value="edu"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="毕业院校">
                <el-input v-model="accountForm.representative_graduated_school" placeholder="请输入毕业院校（可选）" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所学专业">
                <el-input v-model="accountForm.representative_major" placeholder="请输入所学专业（可选）" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 工作人员信息 -->
        <div v-if="accountForm.role === 'staff'">
          <h4 style="margin: 20px 0 15px 0; color: var(--el-text-color-primary);">工作人员信息</h4>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="staff_name">
                <el-input v-model="accountForm.staff_name" placeholder="请输入工作人员姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职位" prop="staff_position">
                <el-select v-model="accountForm.staff_position" placeholder="请选择职位" style="width: 100%">
                  <el-option
                    v-for="position in STAFF_POSITION_OPTIONS"
                    :key="position.value"
                    :label="position.label"
                    :value="position.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="手机号码" prop="staff_mobile_phone">
                <el-input v-model="accountForm.staff_mobile_phone" placeholder="请输入手机号码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱地址">
                <el-input v-model="accountForm.staff_email" placeholder="请输入邮箱地址（可选）" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="工作站点" prop="staff_station_name">
            <el-input v-model="accountForm.staff_station_name" placeholder="请输入工作站点名称" />
          </el-form-item>
        </div>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAccount" :loading="saving">
            创建账号
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordVisible"
      title="重置密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="用户">
          <el-input :value="getUserDisplayName(selectedAccount) + ' (' + selectedAccount?.username + ')'" disabled />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmResetPassword" :loading="resetting">
            确认重置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="用户详情"
      width="600px"
    >
      <div v-if="selectedAccount" class="user-detail">
        <h4>基础信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ selectedAccount.username }}</el-descriptions-item>
          <el-descriptions-item label="角色">{{ selectedAccount.role_display }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedAccount.is_active ? 'success' : 'danger'">
              {{ selectedAccount.is_active ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedAccount.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatDateTime(selectedAccount.last_login_at) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 角色相关信息 -->
        <div v-if="selectedAccount.representative_info" style="margin-top: 20px;">
          <h4>代表信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ selectedAccount.representative_info.name }}</el-descriptions-item>
            <el-descriptions-item label="层级">{{ selectedAccount.representative_info.level }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ selectedAccount.representative_info.gender_display }}</el-descriptions-item>
            <el-descriptions-item label="民族">{{ selectedAccount.representative_info.nationality }}</el-descriptions-item>
            <el-descriptions-item label="所属片区">{{ selectedAccount.representative_info.district || '未填写' }}</el-descriptions-item>
            <el-descriptions-item label="出生日期">{{ selectedAccount.representative_info.birth_date }}</el-descriptions-item>
            <el-descriptions-item label="籍贯">{{ selectedAccount.representative_info.birthplace }}</el-descriptions-item>
            <el-descriptions-item label="党派">{{ selectedAccount.representative_info.party }}</el-descriptions-item>
            <el-descriptions-item label="现任职务">{{ selectedAccount.representative_info.current_position }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ selectedAccount.representative_info.mobile_phone }}</el-descriptions-item>
            <el-descriptions-item label="学历">{{ selectedAccount.representative_info.education }}</el-descriptions-item>
            <el-descriptions-item label="毕业院校">{{ selectedAccount.representative_info.graduated_school || '未填写' }}</el-descriptions-item>
            <el-descriptions-item label="所学专业">{{ selectedAccount.representative_info.major || '未填写' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div v-if="selectedAccount.staff_info" style="margin-top: 20px;">
          <h4>工作人员信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ selectedAccount.staff_info.name }}</el-descriptions-item>
            <el-descriptions-item label="职位">{{ selectedAccount.staff_info.position }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ selectedAccount.staff_info.mobile_phone }}</el-descriptions-item>
            <el-descriptions-item label="邮箱地址">{{ selectedAccount.staff_info.email || '未填写' }}</el-descriptions-item>
            <el-descriptions-item label="工作站点">{{ selectedAccount.staff_info.station_name }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入账号对话框 -->
    <el-dialog
      v-model="importVisible"
      title="导入账号"
      width="600px"
      @close="resetImportForm"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <template #default>
            <div>
              <p>1. 请下载模板文件，按照模板格式填写数据</p>
              <p>2. 支持Excel文件格式（.xlsx）</p>
              <p>3. 必填字段不能为空，请参考模板说明</p>
              <p>4. 用户名必须唯一，不能重复</p>
            </div>
          </template>
        </el-alert>

        <div class="import-actions" style="margin-bottom: 20px;">
          <div style="margin-bottom: 15px; padding: 12px; background-color: var(--el-color-info-light-9); border-radius: 6px; border-left: 4px solid var(--el-color-primary);">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--el-color-primary);">
              <el-icon style="margin-right: 4px;"><InfoFilled /></el-icon>
              请先选择导入类型：
            </label>
            <el-radio-group v-model="importRole" style="margin-top: 8px;">
              <el-radio value="representative" style="margin-right: 20px;">
                <span style="font-weight: 500;">代表账号</span>
              </el-radio>
              <el-radio value="staff">
                <span style="font-weight: 500;">工作人员账号</span>
              </el-radio>
            </el-radio-group>
          </div>
          <el-button
            type="primary"
            @click="downloadTemplate"
            :disabled="!importRole"
            :class="{ 'pulse-animation': !importRole }"
            size="default"
          >
            <el-icon><Download /></el-icon>
            {{ importRole ? `下载${importRole === 'representative' ? '代表' : '工作人员'}模板` : '请先选择导入类型' }}
          </el-button>
        </div>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传Excel文件，且不超过10MB
            </div>
          </template>
        </el-upload>

        <!-- 导入结果显示 -->
        <div v-if="importResult" class="import-result" style="margin-top: 20px;">
          <el-alert
            :title="importResult.success ? '导入成功' : '导入失败'"
            :type="importResult.success ? 'success' : 'error'"
            :closable="false"
          >
            <template #default>
              <div>
                <p>{{ importResult.message }}</p>
                <div v-if="importResult.data">
                  <p>总记录数：{{ importResult.data.total }}</p>
                  <p>成功导入：{{ importResult.data.success_count }}</p>
                  <p>失败记录：{{ importResult.data.error_count }}</p>
                </div>
              </div>
            </template>
          </el-alert>

          <!-- 错误详情 -->
          <div v-if="importResult.data && importResult.data.errors && importResult.data.errors.length > 0" style="margin-top: 10px;">
            <el-collapse>
              <el-collapse-item name="errors">
                <template #title>
                  <span style="color: var(--el-color-danger); font-weight: 500;">
                    <el-icon><WarningFilled /></el-icon>
                    查看错误详情 ({{ importResult.data.errors.length }} 条错误)
                  </span>
                </template>
                <el-table :data="importResult.data.errors" size="small" max-height="300" stripe>
                  <el-table-column prop="row" label="行号" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag type="info" size="small">第{{ row.row }}行</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="field" label="字段" width="120" align="center">
                    <template #default="{ row }">
                      <el-tag type="warning" size="small">{{ row.field }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="value" label="输入值" width="150" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span style="font-family: monospace; background: var(--el-color-info-light-9); padding: 2px 6px; border-radius: 4px;">
                        {{ row.value || '(空值)' }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="error" label="错误说明" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span style="color: var(--el-color-danger);">
                        <el-icon style="margin-right: 4px;"><CircleCloseFilled /></el-icon>
                        {{ row.error }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 错误汇总提示 -->
                <div style="margin-top: 15px; padding: 12px; background: var(--el-color-danger-light-9); border-radius: 6px; border-left: 4px solid var(--el-color-danger);">
                  <div style="font-weight: 500; color: var(--el-color-danger); margin-bottom: 8px;">
                    <el-icon><InfoFilled /></el-icon>
                    解决建议：
                  </div>
                  <ul style="margin: 0; padding-left: 20px; color: var(--el-color-text-regular);">
                    <li>请检查上述错误行的数据格式是否正确</li>
                    <li>确保用户名唯一，不与现有账号重复</li>
                    <li>必填字段（标记*）不能为空</li>
                    <li>修正错误后重新导入即可</li>
                  </ul>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleImport"
            :loading="importLoading"
            :disabled="!selectedFile"
          >
            开始导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, View, Key, Switch, Delete, Download, Upload, InfoFilled, WarningFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { authAPI, userManagementAPI } from '@/api/modules/auth/api'
import {
  REPRESENTATIVE_LEVELS,
  DISTRICT_OPTIONS,
  GENDER_OPTIONS,
  NATIONALITY_OPTIONS,
  PARTY_OPTIONS,
  EDUCATION_OPTIONS,
  STAFF_POSITION_OPTIONS
} from '@/constants/representative'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const resetting = ref(false)
const searchKeyword = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const userList = ref([])
const currentUserId = ref(null)

// 对话框相关
const dialogVisible = ref(false)
const resetPasswordVisible = ref(false)
const detailVisible = ref(false)
const selectedAccount = ref(null)

// 导入导出相关
const importVisible = ref(false)
const importLoading = ref(false)
const selectedFile = ref(null)
const importResult = ref(null)
const uploadRef = ref()
const importRole = ref('representative')

// 表单引用
const accountFormRef = ref()
const passwordFormRef = ref()

// 表单数据
const accountForm = reactive({
  // 基础信息
  username: '',
  password: '',
  confirm_password: '',
  role: '',
  is_active: true,
  
  // 代表信息
  representative_name: '',
  representative_level: '', // 保留向后兼容
  representative_levels: [], // 新的多选层级
  representative_gender: '',
  representative_nationality: '',
  representative_district: '',
  representative_birth_date: '',
  representative_birthplace: '',
  representative_party: '',
  representative_current_position: '',
  representative_mobile_phone: '',
  representative_education: '',
  representative_graduated_school: '',
  representative_major: '',
  
  // 工作人员信息
  staff_name: '',
  staff_position: '',
  staff_mobile_phone: '',
  staff_email: '',
  staff_station_name: ''
})

const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const formRules = computed(() => {
  const baseRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入初始密码', trigger: 'blur' },
      { min: 8, message: '密码长度至少8位', trigger: 'blur' }
    ],
    confirm_password: [
      { required: true, message: '请再次输入密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== accountForm.password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    role: [
      { required: true, message: '请选择角色', trigger: 'change' }
    ]
  }

  // 添加角色相关验证规则
  if (accountForm.role === 'representative') {
    Object.assign(baseRules, {
      // 必填字段：代表层级、姓名、性别、籍贯、出生日期、党派、现任职务、电话
      representative_name: [{ required: true, message: '请输入代表姓名', trigger: 'blur' }],
      representative_levels: [
        {
          required: true,
          type: 'array',
          min: 1,
          message: '请至少选择一个代表层级',
          trigger: 'change'
        }
      ],
      representative_gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
      representative_birth_date: [{ required: true, message: '请选择出生年月', trigger: 'change' }],
      representative_birthplace: [{ required: true, message: '请输入籍贯', trigger: 'blur' }],
      representative_party: [{ required: true, message: '请选择党派', trigger: 'change' }],
      representative_current_position: [{ required: true, message: '请输入现任职务', trigger: 'blur' }],
      representative_mobile_phone: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      // 非必填字段：民族、所属片区、学历、毕业院校、所学专业
      representative_nationality: [{ required: false }],
      representative_district: [{ required: false }],
      representative_education: [{ required: false }]
    })
  } else if (accountForm.role === 'staff') {
    Object.assign(baseRules, {
      staff_name: [{ required: true, message: '请输入工作人员姓名', trigger: 'blur' }],
      staff_position: [{ required: true, message: '请选择职位', trigger: 'change' }],
      staff_mobile_phone: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      staff_station_name: [{ required: true, message: '请输入工作站点名称', trigger: 'blur' }]
    })
  }

  return baseRules
})

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const loadUserList = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    if (roleFilter.value) {
      params.role = roleFilter.value
    }
    
    if (statusFilter.value !== '') {
      params.is_active = statusFilter.value
    }
    
    // 添加搜索关键词支持
    if (searchKeyword.value.trim()) {
      params.search = searchKeyword.value.trim()
    }

    const response = await userManagementAPI.getUserList(params)
    
    if (response.data.success) {
      userList.value = response.data.data.results
      totalCount.value = response.data.data.count
      
      // 如果当前页没有数据且不是第一页，自动跳转到第一页
      if (userList.value.length === 0 && currentPage.value > 1 && totalCount.value > 0) {
        currentPage.value = 1
        loadUserList()
        return
      }
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败：' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  // 重置搜索条件
  searchKeyword.value = ''
  roleFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
  loadUserList()
}

// 搜索防抖定时器
let searchTimer = null

const handleSearchInput = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  // 设置新的定时器，500ms后执行搜索
  searchTimer = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearch = () => {
  // 清除定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
    searchTimer = null
  }
  
  currentPage.value = 1
  loadUserList()
}

const clearFilters = () => {
  searchKeyword.value = ''
  roleFilter.value = ''
  statusFilter.value = ''
  currentPage.value = 1
  loadUserList()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadUserList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadUserList()
}

const showAddDialog = () => {
  dialogVisible.value = true
  resetForm()
}

const handleRoleChange = () => {
  // 清除其他角色的表单数据
  if (accountForm.role === 'representative') {
    // 清除工作人员数据
    accountForm.staff_name = ''
    accountForm.staff_position = ''
    accountForm.staff_mobile_phone = ''
    accountForm.staff_email = ''
    accountForm.staff_station_name = ''
  } else if (accountForm.role === 'staff') {
    // 清除代表数据
    accountForm.representative_name = ''
    accountForm.representative_level = ''
    accountForm.representative_gender = ''
    accountForm.representative_nationality = ''
    accountForm.representative_district = ''
    accountForm.representative_birth_date = ''
    accountForm.representative_birthplace = ''
    accountForm.representative_party = ''
    accountForm.representative_current_position = ''
    accountForm.representative_mobile_phone = ''
    accountForm.representative_education = ''
    accountForm.representative_graduated_school = ''
    accountForm.representative_major = ''
  }
}

const resetForm = () => {
  if (accountFormRef.value) {
    accountFormRef.value.resetFields()
  }
  
  // 重置表单数据
  Object.keys(accountForm).forEach(key => {
    if (key === 'is_active') {
      accountForm[key] = true
    } else {
      accountForm[key] = ''
    }
  })
}

const saveAccount = async () => {
  if (!accountFormRef.value) return
  
  try {
    await accountFormRef.value.validate()
    saving.value = true
    
    // 准备提交的数据，只包含相关角色的字段
    const submitData = {
      username: accountForm.username,
      password: accountForm.password,
      confirm_password: accountForm.confirm_password,
      role: accountForm.role,
      is_active: accountForm.is_active
    }
    
    // 根据角色添加相应的字段
    if (accountForm.role === 'representative') {
      // 格式化出生日期（月份精确度，补充为该月第一天）
      let birthDate = accountForm.representative_birth_date
      if (birthDate) {
        // 如果是YYYY-MM格式，补充为YYYY-MM-01
        if (birthDate.match(/^\d{4}-\d{2}$/)) {
          birthDate = birthDate + '-01'
        }
      }

      // 添加代表相关字段
      Object.assign(submitData, {
        representative_level: accountForm.representative_level,
        representative_name: accountForm.representative_name,
        representative_gender: accountForm.representative_gender,
        representative_nationality: accountForm.representative_nationality,
        representative_district: accountForm.representative_district,
        representative_birth_date: birthDate,
        representative_birthplace: accountForm.representative_birthplace,
        representative_party: accountForm.representative_party,
        representative_current_position: accountForm.representative_current_position,
        representative_mobile_phone: accountForm.representative_mobile_phone,
        representative_education: accountForm.representative_education,
        representative_graduated_school: accountForm.representative_graduated_school || '',
        representative_major: accountForm.representative_major || ''
      })
    } else if (accountForm.role === 'staff') {
      // 添加工作人员相关字段
      Object.assign(submitData, {
        staff_name: accountForm.staff_name,
        staff_position: accountForm.staff_position,
        staff_mobile_phone: accountForm.staff_mobile_phone,
        staff_email: accountForm.staff_email || '',
        staff_station_name: accountForm.staff_station_name
      })
    }
    
    const response = await userManagementAPI.createAccount(submitData)
    
    if (response.data.success) {
      ElMessage.success('创建账号成功')
      dialogVisible.value = false
      loadUserList()
    } else {
      ElMessage.error(response.data.message || '创建账号失败')
    }
  } catch (error) {
    console.error('创建账号失败:', error)
    if (error.response?.data?.errors) {
      // 显示具体的验证错误
      const errors = error.response.data.errors
      
      // 特别处理用户名重复错误
      if (errors.username) {
        const usernameError = Array.isArray(errors.username) ? errors.username[0] : errors.username
        ElMessage.error(usernameError)
        
        // 聚焦到用户名输入框
        if (accountFormRef.value) {
          accountFormRef.value.validateField('username')
        }
      } else {
        // 显示其他验证错误
        let errorMsg = '创建账号失败：\n'
        Object.keys(errors).forEach(field => {
          const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]]
          errorMsg += `${fieldErrors.join(', ')}\n`
        })
        ElMessage.error(errorMsg)
      }
    } else {
      ElMessage.error('创建账号失败：' + (error.response?.data?.message || error.message))
    }
  } finally {
    saving.value = false
  }
}

const viewDetails = async (user) => {
  try {
    const response = await userManagementAPI.getUserDetail(user.id)
    if (response.data.success) {
      selectedAccount.value = response.data.data
      detailVisible.value = true
    } else {
      ElMessage.error('获取用户详情失败')
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败：' + (error.response?.data?.message || error.message))
  }
}

const resetPassword = (user) => {
  selectedAccount.value = user
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  resetPasswordVisible.value = true
}

const confirmResetPassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    resetting.value = true
    
    const response = await userManagementAPI.resetUserPassword(selectedAccount.value.id, {
      new_password: passwordForm.newPassword,
      confirm_password: passwordForm.confirmPassword
    })
    
    if (response.data.success) {
      ElMessage.success(response.data.message || '密码重置成功')
      resetPasswordVisible.value = false
      // 重置表单
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    } else {
      ElMessage.error(response.data.message || '密码重置失败')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    ElMessage.error('密码重置失败：' + (error.response?.data?.message || error.message))
  } finally {
    resetting.value = false
  }
}

const toggleAccountStatus = async (user) => {
  try {
    // 防止禁用自己的账号
    if (user.id === currentUserId.value && user.is_active) {
      ElMessage.warning('不能禁用自己的账号')
      return
    }
    
    const action = user.is_active ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}用户"${getUserDisplayName(user)}"的账号吗？`,
      `${action}账号`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await userManagementAPI.patchUser(user.id, {
      is_active: !user.is_active
    })

    if (response.data.success) {
      ElMessage.success(`账号${action}成功`)
      loadUserList()
    } else {
      ElMessage.error(response.data.message || `账号${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新账号状态失败:', error)
      ElMessage.error('更新账号状态失败：' + (error.response?.data?.message || error.message))
    }
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    const userName = getUserDisplayName(user)
    const userRole = user.role === 'representative' ? '人大代表' : '工作人员'
    
    await ElMessageBox.confirm(
      `确定要删除${userRole}"${userName}"的账号吗？删除后无法恢复！`,
      '删除账号',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const response = await userManagementAPI.deleteUser(user.id)

    if (response.data.success) {
      ElMessage.success(response.data.message || '账号删除成功')
      loadUserList()
    } else {
      ElMessage.error(response.data.message || '账号删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除账号失败:', error)
      ElMessage.error('删除账号失败：' + (error.response?.data?.message || error.message))
    }
  }
}

// 工具方法
const getUserDisplayName = (user) => {
  if (user.representative_info) {
    return user.representative_info.name
  } else if (user.staff_info) {
    return user.staff_info.name
  }
  return user.username
}

const getUserDepartment = (user) => {
  if (user.representative_info) {
    return user.representative_info.level
  } else if (user.staff_info) {
    return user.staff_info.station_name
  }
  return '未设置'
}

const getUserPhone = (user) => {
  if (user.representative_info) {
    return user.representative_info.mobile_phone
  } else if (user.staff_info) {
    return user.staff_info.mobile_phone
  }
  return '未设置'
}

const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '未登录'
  
  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateTimeStr
  }
}

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    const response = await authAPI.getUserProfile()
    if (response.data.success) {
      currentUserId.value = response.data.data.id
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
  }
}

// 导入导出相关方法
const showImportDialog = (role = 'representative') => {
  importRole.value = role
  importVisible.value = true
  resetImportForm()
}

const resetImportForm = () => {
  selectedFile.value = null
  importResult.value = null
  // 如果没有选择导入类型，设置默认值
  if (!importRole.value) {
    importRole.value = 'representative'
  }
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const downloadTemplate = async () => {
  // 检查是否选择了导入类型
  if (!importRole.value || importRole.value.trim() === '') {
    ElMessage({
      message: '请先选择导入类型（代表账号或工作人员账号）',
      type: 'warning',
      duration: 3000,
      showClose: true
    })
    return
  }

  // 验证导入类型是否有效
  if (!['representative', 'staff'].includes(importRole.value)) {
    ElMessage({
      message: '导入类型无效，请重新选择',
      type: 'error',
      duration: 3000,
      showClose: true
    })
    return
  }

  try {
    const response = await userManagementAPI.downloadTemplate(importRole.value)

    // 检查响应是否为有效的文件
    if (!response.data || response.data.size === 0) {
      ElMessage({
        message: '模板文件为空，请联系管理员',
        type: 'error',
        duration: 3000,
        showClose: true
      })
      return
    }

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    const roleText = importRole.value === 'representative' ? '代表' : '工作人员'
    link.download = `${roleText}账号导入模板.xlsx`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage({
      message: `${roleText}模板下载成功`,
      type: 'success',
      duration: 2000,
      showClose: true
    })
  } catch (error) {
    console.error('下载模板失败:', error)

    // 详细的错误处理
    let errorMessage = '下载模板失败'

    if (error.response) {
      // 服务器返回了错误响应
      if (error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message
      } else if (error.response.status === 404) {
        errorMessage = '模板文件不存在，请联系管理员'
      } else if (error.response.status === 403) {
        errorMessage = '权限不足，无法下载模板'
      } else if (error.response.status >= 500) {
        errorMessage = '服务器错误，请稍后重试'
      } else {
        errorMessage = `请求失败（状态码：${error.response.status}）`
      }
    } else if (error.request) {
      // 请求发送了但没有收到响应
      errorMessage = '网络连接失败，请检查网络连接'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 4000,
      showClose: true
    })
  }
}

const handleFileChange = (file) => {
  selectedFile.value = file.raw
  importResult.value = null
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

const handleImport = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要导入的Excel文件')
    return
  }

  try {
    importLoading.value = true
    const response = await userManagementAPI.importUsers(selectedFile.value)

    importResult.value = response.data

    if (response.data.success) {
      ElMessage.success('导入完成')
      // 如果有成功导入的记录，刷新列表
      if (response.data.data.success_count > 0) {
        loadUserList()
      }
    } else {
      ElMessage.error('导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    importResult.value = {
      success: false,
      message: '导入失败：' + (error.response?.data?.message || error.message),
      data: error.response?.data?.data || null
    }
    ElMessage.error('导入失败：' + (error.response?.data?.message || error.message))
  } finally {
    importLoading.value = false
  }
}

const exportUsers = async (exportRole = null) => {
  try {
    const params = {}

    // 如果指定了导出角色，使用指定的角色，否则使用当前筛选条件
    if (exportRole) {
      params.role = exportRole
    } else if (roleFilter.value) {
      params.role = roleFilter.value
    }

    if (statusFilter.value !== '') {
      params.is_active = statusFilter.value
    }

    const response = await userManagementAPI.exportUsers(params)

    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    let roleText = ''
    if (exportRole === 'representative') {
      roleText = '_代表'
    } else if (exportRole === 'staff') {
      roleText = '_工作人员'
    } else if (roleFilter.value === 'representative') {
      roleText = '_代表'
    } else if (roleFilter.value === 'staff') {
      roleText = '_工作人员'
    }

    link.download = `用户列表${roleText}_${timestamp}.xlsx`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败：' + (error.response?.data?.message || error.message))
  }
}

// 处理导出下拉菜单命令
const handleExportCommand = (command) => {
  if (command === 'all') {
    exportUsers()
  } else {
    exportUsers(command)
  }
}

// 处理导入下拉菜单命令
const handleImportCommand = (command) => {
  showImportDialog(command)
}

// 组件挂载时的初始化
onMounted(() => {
  getCurrentUser()
  loadUserList()
})
</script>

<style scoped>
.account-management {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.page-header {
  flex-shrink: 0;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 5px 0;
  color: var(--el-text-color-primary);
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.toolbar {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
  padding: 15px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  padding: 15px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  margin-bottom: 20px;
}

/* 按钮样式统一 */
.toolbar .el-button {
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.toolbar .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.toolbar .el-button .el-icon {
  margin-right: 6px;
}

/* 导入导出按钮特殊样式 */
.toolbar-right .el-button {
  min-width: 100px;
}

.toolbar-right .el-button[type="success"] {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
}

.toolbar-right .el-button[type="warning"] {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
  border: none;
}

.account-list-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.account-list-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 20px;
  overflow: hidden;
}

/* 表格容器 - 占用剩余空间并允许内部滚动 */
.account-list-card :deep(.el-table) {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
}

/* 表格体滚动 - 使用flex自动计算高度 */
.account-list-card :deep(.el-table .el-table__body-wrapper) {
  overflow-y: auto;
  flex: 1;
}

.account-list-card :deep(.el-table__body) {
  padding-bottom: 10px;
}

.account-list-card :deep(.el-table__row:last-child td) {
  border-bottom: 1px solid var(--el-border-color-lighter);
}

/* 分页容器 - 固定在底部 */
.pagination-container {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 20px 0 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 20px;
  min-height: 60px;
}

.pagination-info {
  flex: 1;
  min-width: 200px;
}

.pagination-total {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.pagination-container .el-pagination {
  flex-shrink: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.user-detail h4 {
  color: var(--el-text-color-primary);
  margin: 0 0 10px 0;
}

.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 5px;
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.table-actions .el-button {
  margin: 0;
  white-space: nowrap;
}

/* 表格样式优化 */

.account-list-card :deep(.el-table th) {
  background-color: var(--el-fill-color-light);
  font-weight: 600;
}

.account-list-card :deep(.el-table tbody tr:hover > td) {
  background-color: var(--el-fill-color-extra-light);
}

.account-list-card :deep(.el-table .el-button) {
  font-size: 12px;
}

.account-list-card :deep(.el-table .el-button + .el-button) {
  margin-left: 0;
}

/* 状态标签样式优化 */
.account-list-card :deep(.el-tag) {
  font-weight: 500;
  border-radius: 12px;
  padding: 2px 8px;
}

/* 角色标签样式 */
.account-list-card :deep(.el-table .role-tag) {
  font-size: 12px;
  font-weight: 500;
}

/* 空数据状态优化 */
.account-list-card :deep(.el-table__empty-block) {
  padding: 60px 0;
}

.account-list-card :deep(.el-table__empty-text) {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 加载状态优化 */
.account-list-card :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 分页响应式 */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .pagination-info {
    order: 2;
    margin-top: 10px;
    text-align: center;
  }
  
  .pagination-container .el-pagination {
    order: 1;
    justify-content: center;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-management {
    padding: 10px;
    height: 100vh;
    overflow: hidden;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-left .el-button,
  .toolbar-right .el-button {
    flex: 1;
    min-width: 100px;
  }

  .search-filters {
    flex-direction: column;
    gap: 10px;
  }

  .search-filters .el-select,
  .search-filters .el-input {
    width: 100% !important;
  }

  /* 小屏幕下表格滚动优化 */

  /* 小屏幕下操作按钮优化 */
  .table-actions {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }

  .table-actions .el-button {
    font-size: 11px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  .toolbar-left .el-button,
  .toolbar-right .el-button {
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* 导入对话框样式优化 */
.import-content .el-radio-group .el-radio {
  margin-bottom: 8px;
}

.import-content .el-radio-group .el-radio__label {
  font-weight: 500;
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* 导入类型选择区域样式 */
.import-actions .el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.import-actions .el-radio {
  margin: 0;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.import-actions .el-radio:hover {
  background-color: var(--el-color-primary-light-9);
}

.import-actions .el-radio.is-checked {
  background-color: var(--el-color-primary-light-8);
  border: 1px solid var(--el-color-primary-light-5);
}

.form-tip {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-tip .el-icon {
  margin-right: 4px;
  font-size: 14px;
}
</style> 