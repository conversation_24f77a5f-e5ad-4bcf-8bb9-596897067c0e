<template>
  <div ref="myChart" style="width: 100%; height: 90%"></div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.initChart()
  },
  watch: {
    data: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  methods: {
    async initChart() {
      const myChart = this.$echarts.init(this.$refs.myChart)
      const option = {
        legend: {
          orient: 'vertical',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 8,
          textStyle: {
            color: '#ffffff',
            fontSize: 15,
          },
          top: '20%',
          right: 30,
          data: this.data.map((item) => item.name),
        },
        color: [
          '#37a2da',
          '#32c5e9',
          '#9fe6b8',
          '#ffdb5c',
          '#ff9f7f',
          '#fb7293',
          '#e7bcf3',
          '#8378ea',
        ],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)',
        },

        calculable: true,
        series: [
          {
            type: 'pie',
            radius: [0,'80%'],
            center: ['40%', '50%'],
            data: this.data,
            label: {
              show: true,
              position: 'outside',
              formatter: '{d}%',
              fontSize: 15,
              color: '#ffffff',
              distanceToLabelLine: 3,
            },
            labelLine: {
              show: true,
              length: 8,
              length2: 12,
              lineStyle: {
                width: 1,
                color: 'rgba(255, 255, 255, 0.8)'
              },
            },
            itemStyle: {
              normal: {
                shadowBlur: 20,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
              },
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 30,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      }
      await myChart.setOption(option)
    },
  },
}
</script>

<style scoped></style>
