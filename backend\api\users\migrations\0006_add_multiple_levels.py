# Generated manually for multiple levels support

from django.db import migrations, models
import json


def migrate_single_to_multiple_levels(apps, schema_editor):
    """将单选层级数据迁移到多选层级"""
    Representative = apps.get_model('users', 'Representative')
    
    for rep in Representative.objects.all():
        if rep.level and not rep.levels:
            # 将单选层级转换为多选层级列表
            rep.levels = [rep.level]
            rep.save()
    
    print(f"已迁移 {Representative.objects.count()} 个代表的层级数据")


def reverse_migrate_multiple_to_single_levels(apps, schema_editor):
    """回滚：将多选层级数据迁移回单选层级"""
    Representative = apps.get_model('users', 'Representative')
    
    for rep in Representative.objects.all():
        if rep.levels and not rep.level:
            # 取第一个层级作为单选层级
            rep.level = rep.levels[0] if rep.levels else None
            rep.save()
    
    print(f"已回滚 {Representative.objects.count()} 个代表的层级数据")


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_update_representative_levels'),
    ]

    operations = [
        # 添加新的多选层级字段
        migrations.AddField(
            model_name='representative',
            name='levels',
            field=models.JSONField(default=list, help_text='代表的层级列表，支持多选：乡镇、县区、市、自治区、全国', verbose_name='代表层级（多选）'),
        ),
        
        # 修改原有层级字段为可选
        migrations.AlterField(
            model_name='representative',
            name='level',
            field=models.CharField(blank=True, choices=[('乡镇人大代表', '乡镇人大代表'), ('县区人大代表', '县区人大代表'), ('市人大代表', '市人大代表'), ('自治区人大代表', '自治区人大代表'), ('全国人大代表', '全国人大代表')], help_text='代表的层级：乡镇、县区、市、自治区、全国（此字段已废弃，请使用levels字段）', max_length=50, null=True, verbose_name='代表层级（单选，已废弃）'),
        ),
        
        # 数据迁移：将单选层级转换为多选层级
        migrations.RunPython(
            migrate_single_to_multiple_levels,
            reverse_migrate_multiple_to_single_levels
        ),
    ]
