/**
 * 人大代表相关配置常量
 */

// 代表层级选项
export const REPRESENTATIVE_LEVELS = [
  { label: '乡镇人大代表', value: '乡镇人大代表' },
  { label: '县区人大代表', value: '县区人大代表' },
  { label: '市人大代表', value: '市人大代表' },
  { label: '自治区人大代表', value: '自治区人大代表' },
  { label: '全国人大代表', value: '全国人大代表' }
]

// 层级显示工具函数
export const formatLevelsDisplay = (levels) => {
  if (!levels || !Array.isArray(levels) || levels.length === 0) {
    return '未设置'
  }
  return levels.join('、')
}

// 验证层级数据
export const validateLevels = (levels) => {
  if (!Array.isArray(levels)) {
    return false
  }
  const validLevels = REPRESENTATIVE_LEVELS.map(item => item.value)
  return levels.every(level => validLevels.includes(level))
}

// 所属片区选项
export const DISTRICT_OPTIONS = [
  { label: '那洪片区', value: '那洪片区' },
  { label: '那历片区', value: '那历片区' },
  { label: '沛鸿片区', value: '沛鸿片区' }
]

// 性别选项
export const GENDER_OPTIONS = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
]

// 民族选项
export const NATIONALITY_OPTIONS = [
  '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族', '朝鲜族',
  '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族', '黎族', '傈僳族',
  '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族', '景颇族', '柯尔克孜族',
  '土族', '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族', '毛南族', '仡佬族', '锡伯族',
  '阿昌族', '普米族', '塔吉克族', '怒族', '乌孜别克族', '俄罗斯族', '鄂温克族', '德昂族',
  '保安族', '裕固族', '京族', '塔塔尔族', '独龙族', '鄂伦春族', '赫哲族', '门巴族',
  '珞巴族', '基诺族'
]

// 党派选项
export const PARTY_OPTIONS = [
  '中国共产党', '中国国民党革命委员会', '中国民主同盟', '中国民主建国会', '中国民主促进会',
  '中国农工民主党', '中国致公党', '九三学社', '台湾民主自治同盟', '群众'
]

// 学历选项
export const EDUCATION_OPTIONS = [
  '博士研究生', '硕士研究生', '大学本科', '大学专科', '中等专业学校', '技工学校',
  '高中', '初中', '小学', '其他'
]

// 工作人员职位选项
export const STAFF_POSITION_OPTIONS = [
  { label: '站点主任', value: '站点主任' },
  { label: '副站长', value: '副站长' },
  { label: '工作人员', value: '工作人员' },
  { label: '联络员', value: '联络员' },
  { label: '秘书', value: '秘书' }
]
