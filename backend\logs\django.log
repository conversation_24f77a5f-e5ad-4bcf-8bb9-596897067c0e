INFO 2025-07-24 23:07:55,764 autoreload 12072 11144 Watching for file changes with StatReloader
WARNING 2025-07-24 23:08:07,847 log 12072 5560 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-24 23:08:07,850 basehttp 12072 5560 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-24 23:08:07,898 basehttp 12072 12944 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 483
INFO 2025-07-24 23:08:07,939 basehttp 12072 14204 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-24 23:08:08,007 basehttp 12072 12316 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364676
INFO 2025-07-24 23:19:19,685 basehttp 12072 11292 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364676
INFO 2025-07-24 23:19:27,386 views 12072 6016 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-24 23:19:27,390 basehttp 12072 6016 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-24 23:19:27,524 basehttp 12072 13012 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-24 23:19:29,302 basehttp 12072 6100 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-24 23:19:29,381 basehttp 12072 6188 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-24 23:19:32,674 basehttp 12072 9028 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-24 23:19:32,683 basehttp 12072 10472 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-24 23:19:35,013 basehttp 12072 3564 "GET /api/v1/users/manage/61/ HTTP/1.1" 200 1088
INFO 2025-07-24 23:19:55,298 basehttp 12072 5348 "GET /api/v1/users/manage/58/ HTTP/1.1" 200 935
WARNING 2025-07-24 23:23:12,811 log 12072 10520 Unauthorized: /api/v1/aiknowledge/chat/sse/
WARNING 2025-07-24 23:23:12,813 basehttp 12072 10520 "GET /api/v1/aiknowledge/chat/sse/ HTTP/1.1" 401 6
INFO 2025-07-24 23:23:12,817 services 12072 3720 开始生成SSE流，查询: 1
INFO 2025-07-24 23:23:12,817 services 12072 3720 inputs: {}
INFO 2025-07-24 23:23:12,817 services 12072 3720 query: 1
INFO 2025-07-24 23:23:12,817 services 12072 3720 发送SSE请求到Dify: https://dify.gxaigc.cn/v1/chat-messages
INFO 2025-07-24 23:23:23,054 services 12072 3720 开始处理Dify流式响应
INFO 2025-07-24 23:23:27,369 basehttp 12072 3056 "GET /api/v1/workplan/?page=1&page_size=10 HTTP/1.1" 200 1416
INFO 2025-07-24 23:23:27,379 basehttp 12072 3836 "GET /api/v1/workplan/statistics/ HTTP/1.1" 200 718
INFO 2025-07-24 23:23:29,799 views 12072 3700 用户 admin 查看意见建议列表，返回 10 条记录
INFO 2025-07-24 23:23:29,828 basehttp 12072 3784 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-24 23:23:29,880 basehttp 12072 3700 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 31879
INFO 2025-07-24 23:23:32,669 views 12072 1748 工作人员 管理员 查看 2025年 代表工作总结列表，共 30 位代表
INFO 2025-07-24 23:23:32,673 basehttp 12072 1748 "GET /api/v1/ai-summaries/representatives/?year=2025 HTTP/1.1" 200 356814
INFO 2025-07-24 23:23:36,643 views 12072 13168 工作人员 管理员 查看 2026年 代表工作总结列表，共 30 位代表
INFO 2025-07-24 23:23:36,649 basehttp 12072 13168 "GET /api/v1/ai-summaries/representatives/?year=2026 HTTP/1.1" 200 356771
INFO 2025-07-24 23:23:38,961 views 12072 5256 工作人员 管理员 查看 2025年 代表工作总结列表，共 30 位代表
INFO 2025-07-24 23:23:38,966 basehttp 12072 5256 "GET /api/v1/ai-summaries/representatives/?year=2025 HTTP/1.1" 200 356814
INFO 2025-07-24 23:23:45,382 services 12072 3720 转发参考文件数据: 5条
INFO 2025-07-24 23:23:45,382 services 12072 3720 消息结束
INFO 2025-07-24 23:23:45,393 basehttp 12072 3720 "POST /api/v1/aiknowledge/chat/sse/ HTTP/1.1" 200 113728
INFO 2025-07-24 23:23:48,066 basehttp 12072 11372 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-24 23:23:48,081 basehttp 12072 472 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-24 23:28:54,563 basehttp 12072 7664 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-24 23:28:54,781 basehttp 12072 10144 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-24 23:28:54,800 basehttp 12072 8676 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-24 23:28:54,802 basehttp 12072 1844 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-24 23:28:54,841 basehttp 12072 14632 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-24 23:29:54,822 autoreload 12072 11144 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-24 23:29:55,724 autoreload 11208 14952 Watching for file changes with StatReloader
INFO 2025-07-24 23:30:50,510 autoreload 11208 14952 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-24 23:30:51,122 autoreload 13992 10332 Watching for file changes with StatReloader
INFO 2025-07-24 23:31:14,555 autoreload 13992 10332 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-24 23:31:15,156 autoreload 3732 11468 Watching for file changes with StatReloader
INFO 2025-07-24 23:31:47,975 autoreload 3732 11468 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-24 23:31:48,561 autoreload 14684 13192 Watching for file changes with StatReloader
INFO 2025-07-24 23:32:32,938 autoreload 14684 13192 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-24 23:32:33,525 autoreload 6660 11960 Watching for file changes with StatReloader
INFO 2025-07-24 23:32:50,778 autoreload 6660 11960 C:\code\NPC\backend\api\users\urls.py changed, reloading.
INFO 2025-07-24 23:32:51,377 autoreload 10444 5212 Watching for file changes with StatReloader
INFO 2025-07-25 00:15:06,381 basehttp 10444 832 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:15:06,488 basehttp 10444 14924 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:15:06,503 basehttp 10444 11124 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:15:06,509 basehttp 10444 13168 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:15:06,769 basehttp 10444 11880 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:15:38,925 basehttp 10444 10688 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:16:29,692 basehttp 10444 14096 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:16:44,201 basehttp 10444 3488 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:17:07,481 basehttp 10444 7632 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:17:43,409 basehttp 10444 11492 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:18:46,480 basehttp 10444 13224 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:25,040 basehttp 10444 8940 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:25,253 basehttp 10444 10428 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:25,262 basehttp 10444 11492 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:25,268 basehttp 10444 12532 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:19:25,300 basehttp 10444 6804 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
WARNING 2025-07-25 00:19:30,740 log 10444 1820 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-25 00:19:30,741 basehttp 10444 1820 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
WARNING 2025-07-25 00:19:30,902 log 10444 14652 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-25 00:19:30,904 basehttp 10444 14652 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
WARNING 2025-07-25 00:19:30,909 log 10444 11868 Unauthorized: /api/v1/users/manage/
WARNING 2025-07-25 00:19:30,912 log 10444 9976 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-25 00:19:30,912 basehttp 10444 11868 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 401 171
WARNING 2025-07-25 00:19:30,912 basehttp 10444 9976 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-25 00:19:30,959 basehttp 10444 8916 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 486
INFO 2025-07-25 00:19:31,019 basehttp 10444 10652 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:31,019 basehttp 10444 7632 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:31,023 basehttp 10444 10788 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:31,058 basehttp 10444 14452 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:19:31,110 basehttp 10444 13580 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:19:35,784 basehttp 10444 9176 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:35,981 basehttp 10444 2640 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:35,995 basehttp 10444 10976 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:36,000 basehttp 10444 3056 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:19:36,076 basehttp 10444 12284 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:19:48,761 basehttp 10444 6508 "GET /api/v1/people-opinions/?page=1&page_size=20&search= HTTP/1.1" 200 10851
INFO 2025-07-25 00:19:49,864 basehttp 10444 7052 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:19:49,878 basehttp 10444 1936 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:19:56,663 basehttp 10444 4664 "GET /api/v1/users/manage/?page=1&page_size=20&role=representative HTTP/1.1" 200 6773
INFO 2025-07-25 00:19:59,233 basehttp 10444 6404 "GET /api/v1/users/manage/?page=1&page_size=20&role=staff HTTP/1.1" 200 7754
INFO 2025-07-25 00:20:00,772 basehttp 10444 13416 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:21:30,510 basehttp 10444 11724 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:21:46,618 basehttp 10444 11300 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:22:08,159 basehttp 10444 14972 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:22:37,601 basehttp 10444 3628 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:22:37,843 basehttp 10444 5904 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:22:37,860 basehttp 10444 5780 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:22:37,862 basehttp 10444 12776 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:22:38,119 basehttp 10444 332 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:23:01,649 basehttp 10444 13016 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:23:01,870 basehttp 10444 14472 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:23:01,898 basehttp 10444 4184 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:23:01,908 basehttp 10444 6632 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:23:02,234 basehttp 10444 12308 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
WARNING 2025-07-25 00:23:18,075 signals 10444 8700 用户即将删除 - ID: 57, 用户名: test_representative, 角色: 人大代表
INFO 2025-07-25 00:23:18,082 views 10444 8700 工作人员 admin 删除了用户 test_representative（人大代表）
INFO 2025-07-25 00:23:18,083 basehttp 10444 8700 "DELETE /api/v1/users/manage/57/ HTTP/1.1" 200 76
INFO 2025-07-25 00:23:18,157 basehttp 10444 7056 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6887
WARNING 2025-07-25 00:23:20,481 signals 10444 12708 用户即将删除 - ID: 56, 用户名: representative, 角色: 人大代表
INFO 2025-07-25 00:23:20,489 views 10444 12708 工作人员 admin 删除了用户 representative（人大代表）
INFO 2025-07-25 00:23:20,490 basehttp 10444 12708 "DELETE /api/v1/users/manage/56/ HTTP/1.1" 200 71
INFO 2025-07-25 00:23:20,523 basehttp 10444 5336 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6854
WARNING 2025-07-25 00:23:22,619 signals 10444 9940 用户即将删除 - ID: 55, 用户名: test_rep_3, 角色: 人大代表
INFO 2025-07-25 00:23:22,629 views 10444 9940 工作人员 admin 删除了用户 test_rep_3（人大代表）
INFO 2025-07-25 00:23:22,630 basehttp 10444 9940 "DELETE /api/v1/users/manage/55/ HTTP/1.1" 200 67
INFO 2025-07-25 00:23:22,658 basehttp 10444 15208 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6851
WARNING 2025-07-25 00:23:25,443 signals 10444 14188 用户即将删除 - ID: 54, 用户名: test_rep_2, 角色: 人大代表
INFO 2025-07-25 00:23:25,453 views 10444 14188 工作人员 admin 删除了用户 test_rep_2（人大代表）
INFO 2025-07-25 00:23:25,456 basehttp 10444 14188 "DELETE /api/v1/users/manage/54/ HTTP/1.1" 200 67
INFO 2025-07-25 00:23:25,494 basehttp 10444 7496 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6818
WARNING 2025-07-25 00:23:27,978 signals 10444 1984 用户即将删除 - ID: 53, 用户名: test_rep_1, 角色: 人大代表
INFO 2025-07-25 00:23:27,989 views 10444 1984 工作人员 admin 删除了用户 test_rep_1（人大代表）
INFO 2025-07-25 00:23:27,991 basehttp 10444 1984 "DELETE /api/v1/users/manage/53/ HTTP/1.1" 200 67
INFO 2025-07-25 00:23:28,020 basehttp 10444 10220 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6815
WARNING 2025-07-25 00:23:29,919 signals 10444 10612 用户即将删除 - ID: 52, 用户名: rep025, 角色: 人大代表
INFO 2025-07-25 00:23:29,929 views 10444 10612 工作人员 admin 删除了用户 rep025（人大代表）
INFO 2025-07-25 00:23:29,930 basehttp 10444 10612 "DELETE /api/v1/users/manage/52/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:29,959 basehttp 10444 7236 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6815
WARNING 2025-07-25 00:23:31,490 signals 10444 10696 用户即将删除 - ID: 51, 用户名: rep024, 角色: 人大代表
INFO 2025-07-25 00:23:31,496 views 10444 10696 工作人员 admin 删除了用户 rep024（人大代表）
INFO 2025-07-25 00:23:31,498 basehttp 10444 10696 "DELETE /api/v1/users/manage/51/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:31,527 basehttp 10444 10028 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6815
WARNING 2025-07-25 00:23:33,090 signals 10444 10724 用户即将删除 - ID: 50, 用户名: rep023, 角色: 人大代表
INFO 2025-07-25 00:23:33,095 views 10444 10724 工作人员 admin 删除了用户 rep023（人大代表）
INFO 2025-07-25 00:23:33,095 basehttp 10444 10724 "DELETE /api/v1/users/manage/50/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:33,124 basehttp 10444 1844 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6815
WARNING 2025-07-25 00:23:34,639 signals 10444 4732 用户即将删除 - ID: 49, 用户名: rep022, 角色: 人大代表
INFO 2025-07-25 00:23:34,645 views 10444 4732 工作人员 admin 删除了用户 rep022（人大代表）
INFO 2025-07-25 00:23:34,646 basehttp 10444 4732 "DELETE /api/v1/users/manage/49/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:34,686 basehttp 10444 4744 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6815
WARNING 2025-07-25 00:23:36,065 signals 10444 2268 用户即将删除 - ID: 48, 用户名: rep021, 角色: 人大代表
INFO 2025-07-25 00:23:36,073 views 10444 2268 工作人员 admin 删除了用户 rep021（人大代表）
INFO 2025-07-25 00:23:36,075 basehttp 10444 2268 "DELETE /api/v1/users/manage/48/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:36,104 basehttp 10444 14808 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6815
WARNING 2025-07-25 00:23:37,583 signals 10444 10192 用户即将删除 - ID: 47, 用户名: rep020, 角色: 人大代表
INFO 2025-07-25 00:23:37,588 views 10444 10192 工作人员 admin 删除了用户 rep020（人大代表）
INFO 2025-07-25 00:23:37,588 basehttp 10444 10192 "DELETE /api/v1/users/manage/47/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:37,620 basehttp 10444 13568 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6867
WARNING 2025-07-25 00:23:38,965 signals 10444 9340 用户即将删除 - ID: 46, 用户名: rep019, 角色: 人大代表
INFO 2025-07-25 00:23:38,971 views 10444 9340 工作人员 admin 删除了用户 rep019（人大代表）
INFO 2025-07-25 00:23:38,973 basehttp 10444 9340 "DELETE /api/v1/users/manage/46/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:39,003 basehttp 10444 1004 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6919
WARNING 2025-07-25 00:23:40,517 signals 10444 11716 用户即将删除 - ID: 45, 用户名: rep018, 角色: 人大代表
INFO 2025-07-25 00:23:40,525 views 10444 11716 工作人员 admin 删除了用户 rep018（人大代表）
INFO 2025-07-25 00:23:40,526 basehttp 10444 11716 "DELETE /api/v1/users/manage/45/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:40,557 basehttp 10444 14484 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6971
WARNING 2025-07-25 00:23:42,105 signals 10444 14548 用户即将删除 - ID: 44, 用户名: rep017, 角色: 人大代表
INFO 2025-07-25 00:23:42,118 views 10444 14548 工作人员 admin 删除了用户 rep017（人大代表）
INFO 2025-07-25 00:23:42,119 basehttp 10444 14548 "DELETE /api/v1/users/manage/44/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:42,159 basehttp 10444 276 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7023
WARNING 2025-07-25 00:23:43,642 signals 10444 6560 用户即将删除 - ID: 43, 用户名: rep016, 角色: 人大代表
INFO 2025-07-25 00:23:43,648 views 10444 6560 工作人员 admin 删除了用户 rep016（人大代表）
INFO 2025-07-25 00:23:43,648 basehttp 10444 6560 "DELETE /api/v1/users/manage/43/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:43,682 basehttp 10444 13604 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7075
WARNING 2025-07-25 00:23:45,193 signals 10444 14564 用户即将删除 - ID: 42, 用户名: rep015, 角色: 人大代表
INFO 2025-07-25 00:23:45,205 views 10444 14564 工作人员 admin 删除了用户 rep015（人大代表）
INFO 2025-07-25 00:23:45,205 basehttp 10444 14564 "DELETE /api/v1/users/manage/42/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:45,235 basehttp 10444 7292 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7127
WARNING 2025-07-25 00:23:46,586 signals 10444 8048 用户即将删除 - ID: 41, 用户名: rep014, 角色: 人大代表
INFO 2025-07-25 00:23:46,591 views 10444 8048 工作人员 admin 删除了用户 rep014（人大代表）
INFO 2025-07-25 00:23:46,592 basehttp 10444 8048 "DELETE /api/v1/users/manage/41/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:46,623 basehttp 10444 1668 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7179
WARNING 2025-07-25 00:23:47,964 signals 10444 6516 用户即将删除 - ID: 40, 用户名: rep013, 角色: 人大代表
INFO 2025-07-25 00:23:47,969 views 10444 6516 工作人员 admin 删除了用户 rep013（人大代表）
INFO 2025-07-25 00:23:47,970 basehttp 10444 6516 "DELETE /api/v1/users/manage/40/ HTTP/1.1" 200 63
INFO 2025-07-25 00:23:47,994 basehttp 10444 7672 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7231
WARNING 2025-07-25 00:24:02,827 signals 10444 12516 用户即将删除 - ID: 61, 用户名: testuser, 角色: 人大代表
INFO 2025-07-25 00:24:02,834 views 10444 12516 工作人员 admin 删除了用户 testuser（人大代表）
INFO 2025-07-25 00:24:02,836 basehttp 10444 12516 "DELETE /api/v1/users/manage/61/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:02,875 basehttp 10444 14224 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7274
WARNING 2025-07-25 00:24:04,347 signals 10444 12420 用户即将删除 - ID: 60, 用户名: user01, 角色: 人大代表
INFO 2025-07-25 00:24:04,356 views 10444 12420 工作人员 admin 删除了用户 user01（人大代表）
INFO 2025-07-25 00:24:04,358 basehttp 10444 12420 "DELETE /api/v1/users/manage/60/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:04,390 basehttp 10444 14956 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7289
WARNING 2025-07-25 00:24:06,125 signals 10444 10880 用户即将删除 - ID: 39, 用户名: rep012, 角色: 人大代表
INFO 2025-07-25 00:24:06,138 views 10444 10880 工作人员 admin 删除了用户 rep012（人大代表）
INFO 2025-07-25 00:24:06,139 basehttp 10444 10880 "DELETE /api/v1/users/manage/39/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:06,177 basehttp 10444 10052 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7341
WARNING 2025-07-25 00:24:07,729 signals 10444 2468 用户即将删除 - ID: 38, 用户名: rep011, 角色: 人大代表
INFO 2025-07-25 00:24:07,736 views 10444 2468 工作人员 admin 删除了用户 rep011（人大代表）
INFO 2025-07-25 00:24:07,740 basehttp 10444 2468 "DELETE /api/v1/users/manage/38/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:07,787 basehttp 10444 10256 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7393
WARNING 2025-07-25 00:24:09,200 signals 10444 1324 用户即将删除 - ID: 37, 用户名: rep010, 角色: 人大代表
INFO 2025-07-25 00:24:09,208 views 10444 1324 工作人员 admin 删除了用户 rep010（人大代表）
INFO 2025-07-25 00:24:09,209 basehttp 10444 1324 "DELETE /api/v1/users/manage/37/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:09,247 basehttp 10444 7620 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7445
WARNING 2025-07-25 00:24:10,711 signals 10444 10164 用户即将删除 - ID: 36, 用户名: rep009, 角色: 人大代表
INFO 2025-07-25 00:24:10,723 views 10444 10164 工作人员 admin 删除了用户 rep009（人大代表）
INFO 2025-07-25 00:24:10,725 basehttp 10444 10164 "DELETE /api/v1/users/manage/36/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:10,768 basehttp 10444 12256 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7497
WARNING 2025-07-25 00:24:12,268 signals 10444 5452 用户即将删除 - ID: 35, 用户名: rep008, 角色: 人大代表
INFO 2025-07-25 00:24:12,273 views 10444 5452 工作人员 admin 删除了用户 rep008（人大代表）
INFO 2025-07-25 00:24:12,273 basehttp 10444 5452 "DELETE /api/v1/users/manage/35/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:12,300 basehttp 10444 13196 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7549
WARNING 2025-07-25 00:24:13,931 signals 10444 12232 用户即将删除 - ID: 34, 用户名: rep007, 角色: 人大代表
INFO 2025-07-25 00:24:13,937 views 10444 12232 工作人员 admin 删除了用户 rep007（人大代表）
INFO 2025-07-25 00:24:13,937 basehttp 10444 12232 "DELETE /api/v1/users/manage/34/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:13,988 basehttp 10444 11768 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7601
WARNING 2025-07-25 00:24:15,308 signals 10444 7392 用户即将删除 - ID: 33, 用户名: rep006, 角色: 人大代表
INFO 2025-07-25 00:24:15,318 views 10444 7392 工作人员 admin 删除了用户 rep006（人大代表）
INFO 2025-07-25 00:24:15,319 basehttp 10444 7392 "DELETE /api/v1/users/manage/33/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:15,346 basehttp 10444 1408 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7652
WARNING 2025-07-25 00:24:16,856 signals 10444 4980 用户即将删除 - ID: 32, 用户名: rep005, 角色: 人大代表
INFO 2025-07-25 00:24:16,869 views 10444 4980 工作人员 admin 删除了用户 rep005（人大代表）
INFO 2025-07-25 00:24:16,870 basehttp 10444 4980 "DELETE /api/v1/users/manage/32/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:16,899 basehttp 10444 13768 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7703
WARNING 2025-07-25 00:24:18,315 signals 10444 10856 用户即将删除 - ID: 31, 用户名: rep004, 角色: 人大代表
INFO 2025-07-25 00:24:18,315 views 10444 10856 工作人员 admin 删除了用户 rep004（人大代表）
INFO 2025-07-25 00:24:18,315 basehttp 10444 10856 "DELETE /api/v1/users/manage/31/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:18,358 basehttp 10444 3048 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7754
WARNING 2025-07-25 00:24:19,708 signals 10444 13592 用户即将删除 - ID: 30, 用户名: staff025, 角色: 站点工作人员
INFO 2025-07-25 00:24:19,726 views 10444 13592 工作人员 admin 删除了用户 staff025（站点工作人员）
INFO 2025-07-25 00:24:19,726 basehttp 10444 13592 "DELETE /api/v1/users/manage/30/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:19,763 basehttp 10444 13092 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7753
WARNING 2025-07-25 00:24:20,982 signals 10444 5200 用户即将删除 - ID: 29, 用户名: staff024, 角色: 站点工作人员
INFO 2025-07-25 00:24:20,982 views 10444 5200 工作人员 admin 删除了用户 staff024（站点工作人员）
INFO 2025-07-25 00:24:20,982 basehttp 10444 5200 "DELETE /api/v1/users/manage/29/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:21,018 basehttp 10444 5564 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7752
WARNING 2025-07-25 00:24:22,445 signals 10444 1212 用户即将删除 - ID: 28, 用户名: staff023, 角色: 站点工作人员
INFO 2025-07-25 00:24:22,461 views 10444 1212 工作人员 admin 删除了用户 staff023（站点工作人员）
INFO 2025-07-25 00:24:22,461 basehttp 10444 1212 "DELETE /api/v1/users/manage/28/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:22,499 basehttp 10444 4032 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7750
WARNING 2025-07-25 00:24:23,950 signals 10444 4992 用户即将删除 - ID: 27, 用户名: staff022, 角色: 站点工作人员
INFO 2025-07-25 00:24:23,966 views 10444 4992 工作人员 admin 删除了用户 staff022（站点工作人员）
INFO 2025-07-25 00:24:23,966 basehttp 10444 4992 "DELETE /api/v1/users/manage/27/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:24,004 basehttp 10444 13096 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7748
WARNING 2025-07-25 00:24:25,349 signals 10444 2272 用户即将删除 - ID: 26, 用户名: staff021, 角色: 站点工作人员
INFO 2025-07-25 00:24:25,364 views 10444 2272 工作人员 admin 删除了用户 staff021（站点工作人员）
INFO 2025-07-25 00:24:25,364 basehttp 10444 2272 "DELETE /api/v1/users/manage/26/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:25,396 basehttp 10444 12876 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7720
WARNING 2025-07-25 00:24:26,803 signals 10444 5820 用户即将删除 - ID: 25, 用户名: staff020, 角色: 站点工作人员
INFO 2025-07-25 00:24:26,803 views 10444 5820 工作人员 admin 删除了用户 staff020（站点工作人员）
INFO 2025-07-25 00:24:26,803 basehttp 10444 5820 "DELETE /api/v1/users/manage/25/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:26,843 basehttp 10444 11312 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7704
WARNING 2025-07-25 00:24:28,216 signals 10444 2488 用户即将删除 - ID: 24, 用户名: staff019, 角色: 站点工作人员
INFO 2025-07-25 00:24:28,216 views 10444 2488 工作人员 admin 删除了用户 staff019（站点工作人员）
INFO 2025-07-25 00:24:28,216 basehttp 10444 2488 "DELETE /api/v1/users/manage/24/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:28,255 basehttp 10444 4300 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7729
WARNING 2025-07-25 00:24:29,620 signals 10444 11980 用户即将删除 - ID: 23, 用户名: staff018, 角色: 站点工作人员
INFO 2025-07-25 00:24:29,635 views 10444 11980 工作人员 admin 删除了用户 staff018（站点工作人员）
INFO 2025-07-25 00:24:29,635 basehttp 10444 11980 "DELETE /api/v1/users/manage/23/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:29,662 basehttp 10444 10896 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7754
WARNING 2025-07-25 00:24:31,030 signals 10444 2948 用户即将删除 - ID: 22, 用户名: staff017, 角色: 站点工作人员
INFO 2025-07-25 00:24:31,030 views 10444 2948 工作人员 admin 删除了用户 staff017（站点工作人员）
INFO 2025-07-25 00:24:31,030 basehttp 10444 2948 "DELETE /api/v1/users/manage/22/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:31,066 basehttp 10444 9920 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7372
WARNING 2025-07-25 00:24:32,594 signals 10444 7308 用户即将删除 - ID: 21, 用户名: staff016, 角色: 站点工作人员
INFO 2025-07-25 00:24:32,608 views 10444 7308 工作人员 admin 删除了用户 staff016（站点工作人员）
INFO 2025-07-25 00:24:32,608 basehttp 10444 7308 "DELETE /api/v1/users/manage/21/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:32,658 basehttp 10444 11720 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6990
WARNING 2025-07-25 00:24:34,044 signals 10444 13080 用户即将删除 - ID: 20, 用户名: staff015, 角色: 站点工作人员
INFO 2025-07-25 00:24:34,051 views 10444 13080 工作人员 admin 删除了用户 staff015（站点工作人员）
INFO 2025-07-25 00:24:34,051 basehttp 10444 13080 "DELETE /api/v1/users/manage/20/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:34,092 basehttp 10444 8912 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6608
WARNING 2025-07-25 00:24:35,558 signals 10444 14592 用户即将删除 - ID: 19, 用户名: staff014, 角色: 站点工作人员
INFO 2025-07-25 00:24:35,573 views 10444 14592 工作人员 admin 删除了用户 staff014（站点工作人员）
INFO 2025-07-25 00:24:35,573 basehttp 10444 14592 "DELETE /api/v1/users/manage/19/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:35,607 basehttp 10444 4204 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6226
WARNING 2025-07-25 00:24:37,560 signals 10444 15084 用户即将删除 - ID: 18, 用户名: staff013, 角色: 站点工作人员
INFO 2025-07-25 00:24:37,560 views 10444 15084 工作人员 admin 删除了用户 staff013（站点工作人员）
INFO 2025-07-25 00:24:37,560 basehttp 10444 15084 "DELETE /api/v1/users/manage/18/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:37,594 basehttp 10444 5340 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 5844
WARNING 2025-07-25 00:24:38,969 signals 10444 15288 用户即将删除 - ID: 17, 用户名: staff012, 角色: 站点工作人员
INFO 2025-07-25 00:24:38,987 views 10444 15288 工作人员 admin 删除了用户 staff012（站点工作人员）
INFO 2025-07-25 00:24:38,987 basehttp 10444 15288 "DELETE /api/v1/users/manage/17/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:39,026 basehttp 10444 10624 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 5462
WARNING 2025-07-25 00:24:40,485 signals 10444 13672 用户即将删除 - ID: 16, 用户名: staff011, 角色: 站点工作人员
INFO 2025-07-25 00:24:40,485 views 10444 13672 工作人员 admin 删除了用户 staff011（站点工作人员）
INFO 2025-07-25 00:24:40,504 basehttp 10444 13672 "DELETE /api/v1/users/manage/16/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:40,540 basehttp 10444 7980 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 5080
WARNING 2025-07-25 00:24:41,948 signals 10444 1888 用户即将删除 - ID: 15, 用户名: staff010, 角色: 站点工作人员
INFO 2025-07-25 00:24:41,964 views 10444 1888 工作人员 admin 删除了用户 staff010（站点工作人员）
INFO 2025-07-25 00:24:41,964 basehttp 10444 1888 "DELETE /api/v1/users/manage/15/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:42,000 basehttp 10444 5700 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 4698
WARNING 2025-07-25 00:24:43,400 signals 10444 6804 用户即将删除 - ID: 14, 用户名: staff009, 角色: 站点工作人员
INFO 2025-07-25 00:24:43,411 views 10444 6804 工作人员 admin 删除了用户 staff009（站点工作人员）
INFO 2025-07-25 00:24:43,411 basehttp 10444 6804 "DELETE /api/v1/users/manage/14/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:43,441 basehttp 10444 12208 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 4317
WARNING 2025-07-25 00:24:44,822 signals 10444 10176 用户即将删除 - ID: 13, 用户名: staff008, 角色: 站点工作人员
INFO 2025-07-25 00:24:44,838 views 10444 10176 工作人员 admin 删除了用户 staff008（站点工作人员）
INFO 2025-07-25 00:24:44,838 basehttp 10444 10176 "DELETE /api/v1/users/manage/13/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:44,874 basehttp 10444 2156 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 3936
WARNING 2025-07-25 00:24:46,377 signals 10444 11540 用户即将删除 - ID: 12, 用户名: staff007, 角色: 站点工作人员
INFO 2025-07-25 00:24:46,377 views 10444 11540 工作人员 admin 删除了用户 staff007（站点工作人员）
INFO 2025-07-25 00:24:46,377 basehttp 10444 11540 "DELETE /api/v1/users/manage/12/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:46,419 basehttp 10444 14908 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 3554
WARNING 2025-07-25 00:24:48,557 signals 10444 4768 用户即将删除 - ID: 11, 用户名: staff006, 角色: 站点工作人员
INFO 2025-07-25 00:24:48,572 views 10444 4768 工作人员 admin 删除了用户 staff006（站点工作人员）
INFO 2025-07-25 00:24:48,572 basehttp 10444 4768 "DELETE /api/v1/users/manage/11/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:48,610 basehttp 10444 11960 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 3173
WARNING 2025-07-25 00:24:50,256 signals 10444 5180 用户即将删除 - ID: 10, 用户名: staff005, 角色: 站点工作人员
INFO 2025-07-25 00:24:50,265 views 10444 5180 工作人员 admin 删除了用户 staff005（站点工作人员）
INFO 2025-07-25 00:24:50,265 basehttp 10444 5180 "DELETE /api/v1/users/manage/10/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:50,294 basehttp 10444 2292 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 2792
WARNING 2025-07-25 00:24:51,732 signals 10444 2984 用户即将删除 - ID: 9, 用户名: staff004, 角色: 站点工作人员
INFO 2025-07-25 00:24:51,743 views 10444 2984 工作人员 admin 删除了用户 staff004（站点工作人员）
INFO 2025-07-25 00:24:51,743 basehttp 10444 2984 "DELETE /api/v1/users/manage/9/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:51,782 basehttp 10444 11560 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 2412
WARNING 2025-07-25 00:24:54,284 signals 10444 9488 用户即将删除 - ID: 8, 用户名: staff003, 角色: 站点工作人员
INFO 2025-07-25 00:24:54,297 views 10444 9488 工作人员 admin 删除了用户 staff003（站点工作人员）
INFO 2025-07-25 00:24:54,297 basehttp 10444 9488 "DELETE /api/v1/users/manage/8/ HTTP/1.1" 200 65
INFO 2025-07-25 00:24:54,326 basehttp 10444 9676 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 2032
WARNING 2025-07-25 00:24:56,376 signals 10444 3848 用户即将删除 - ID: 5, 用户名: rep003, 角色: 人大代表
INFO 2025-07-25 00:24:56,392 views 10444 3848 工作人员 admin 删除了用户 rep003（人大代表）
INFO 2025-07-25 00:24:56,392 basehttp 10444 3848 "DELETE /api/v1/users/manage/5/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:56,422 basehttp 10444 10464 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 1678
WARNING 2025-07-25 00:24:58,126 signals 10444 5920 用户即将删除 - ID: 3, 用户名: rep001, 角色: 人大代表
INFO 2025-07-25 00:24:58,157 views 10444 5920 工作人员 admin 删除了用户 rep001（人大代表）
INFO 2025-07-25 00:24:58,157 basehttp 10444 5920 "DELETE /api/v1/users/manage/3/ HTTP/1.1" 200 63
INFO 2025-07-25 00:24:58,180 basehttp 10444 15000 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 1312
WARNING 2025-07-25 00:25:00,298 signals 10444 5108 用户即将删除 - ID: 2, 用户名: staff002, 角色: 站点工作人员
INFO 2025-07-25 00:25:00,298 views 10444 5108 工作人员 admin 删除了用户 staff002（站点工作人员）
INFO 2025-07-25 00:25:00,298 basehttp 10444 5108 "DELETE /api/v1/users/manage/2/ HTTP/1.1" 200 65
INFO 2025-07-25 00:25:00,339 basehttp 10444 1144 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 905
WARNING 2025-07-25 00:25:02,200 signals 10444 12368 用户即将删除 - ID: 1, 用户名: staff001, 角色: 站点工作人员
INFO 2025-07-25 00:25:02,218 views 10444 12368 工作人员 admin 删除了用户 staff001（站点工作人员）
INFO 2025-07-25 00:25:02,218 basehttp 10444 12368 "DELETE /api/v1/users/manage/1/ HTTP/1.1" 200 65
INFO 2025-07-25 00:25:02,249 basehttp 10444 13872 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 498
INFO 2025-07-25 00:25:16,385 views 10444 4416 工作人员 admin 重置了用户 admin 的密码
INFO 2025-07-25 00:25:16,385 basehttp 10444 4416 "PUT /api/v1/users/manage/58/ HTTP/1.1" 200 65
INFO 2025-07-25 00:25:18,726 basehttp 10444 4956 "GET /api/v1/users/manage/58/ HTTP/1.1" 200 935
WARNING 2025-07-25 00:25:44,131 log 10444 1844 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:25:44,131 basehttp 10444 1844 "GET /api/v1/api/users/export/ HTTP/1.1" 404 4074
WARNING 2025-07-25 00:25:47,205 log 10444 4904 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:25:47,222 basehttp 10444 4904 "GET /api/v1/api/users/export/ HTTP/1.1" 404 4074
INFO 2025-07-25 00:26:50,775 autoreload 14040 7392 Watching for file changes with StatReloader
INFO 2025-07-25 00:26:54,482 basehttp 14040 11060 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:26:54,576 basehttp 14040 7876 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:26:54,588 basehttp 14040 14688 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:26:54,599 basehttp 14040 11216 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:26:54,651 basehttp 14040 1496 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:26:58,722 basehttp 14040 2564 "GET /api/v1/users/manage/55/ HTTP/1.1" 200 1138
WARNING 2025-07-25 00:27:21,444 log 14040 12664 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:27:21,449 basehttp 14040 12664 "GET /api/v1/api/users/export/ HTTP/1.1" 404 4074
INFO 2025-07-25 00:31:05,544 autoreload 14040 7392 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-25 00:31:06,323 autoreload 12256 1648 Watching for file changes with StatReloader
INFO 2025-07-25 00:31:19,334 autoreload 12256 1648 C:\code\NPC\backend\api\users\urls.py changed, reloading.
INFO 2025-07-25 00:31:19,940 autoreload 1216 4960 Watching for file changes with StatReloader
INFO 2025-07-25 00:32:03,931 autoreload 1216 4960 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-25 00:32:04,685 autoreload 10472 8364 Watching for file changes with StatReloader
INFO 2025-07-25 00:32:22,631 basehttp 10472 2580 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:22,791 basehttp 10472 9724 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:22,807 basehttp 10472 12128 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:22,815 basehttp 10472 1812 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:32:22,884 basehttp 10472 14660 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:32:42,375 basehttp 10472 7488 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:42,608 basehttp 10472 11748 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:42,621 basehttp 10472 7192 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:32:42,637 basehttp 10472 13184 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:42,996 basehttp 10472 6568 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:32:58,691 basehttp 10472 14664 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:58,922 basehttp 10472 1924 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:58,929 basehttp 10472 10320 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:32:58,954 basehttp 10472 2988 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:32:59,207 basehttp 10472 12468 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:33:10,336 basehttp 10472 13568 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:10,656 basehttp 10472 13160 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:10,673 basehttp 10472 1072 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:10,673 basehttp 10472 8920 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:33:10,760 basehttp 10472 13140 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:33:29,925 basehttp 10472 14276 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:30,389 basehttp 10472 5372 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:30,494 basehttp 10472 14888 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:33:30,550 basehttp 10472 8612 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:31,364 basehttp 10472 6660 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:33:55,845 basehttp 10472 2596 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:56,087 basehttp 10472 8828 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:56,093 basehttp 10472 14428 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:33:56,107 basehttp 10472 8868 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:33:56,189 basehttp 10472 11752 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
WARNING 2025-07-25 00:34:12,349 log 10472 5344 Not Found: /api/v1/api/users/template/
WARNING 2025-07-25 00:34:12,350 basehttp 10472 5344 "GET /api/v1/api/users/template/?role=representative HTTP/1.1" 404 4100
INFO 2025-07-25 00:34:14,005 autoreload 10472 8364 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-25 00:34:14,666 autoreload 2588 5716 Watching for file changes with StatReloader
WARNING 2025-07-25 00:34:19,139 log 2588 12612 Not Found: /api/v1/api/users/template/
WARNING 2025-07-25 00:34:19,139 basehttp 2588 12612 "GET /api/v1/api/users/template/?role=representative HTTP/1.1" 404 4100
INFO 2025-07-25 00:34:29,898 autoreload 2588 5716 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-25 00:34:30,654 autoreload 11188 6104 Watching for file changes with StatReloader
WARNING 2025-07-25 00:34:33,915 log 11188 13964 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:34:33,915 basehttp 11188 13964 "GET /api/v1/api/users/export/?role=[object+PointerEvent] HTTP/1.1" 404 4101
INFO 2025-07-25 00:34:48,896 basehttp 11188 592 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:34:49,049 basehttp 11188 6700 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:34:49,068 basehttp 11188 6000 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:34:49,075 basehttp 11188 9888 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:34:49,328 basehttp 11188 9820 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
WARNING 2025-07-25 00:34:55,308 log 11188 1744 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:34:55,308 basehttp 11188 1744 "GET /api/v1/api/users/export/?role=[object+PointerEvent] HTTP/1.1" 404 4101
WARNING 2025-07-25 00:35:00,482 log 11188 10692 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:35:00,482 basehttp 11188 10692 "GET /api/v1/api/users/export/?role=representative HTTP/1.1" 404 4094
WARNING 2025-07-25 00:35:03,081 log 11188 12536 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:35:03,081 basehttp 11188 12536 "GET /api/v1/api/users/export/?role=staff HTTP/1.1" 404 4085
WARNING 2025-07-25 00:35:07,327 log 11188 10428 Not Found: /api/v1/api/users/template/
WARNING 2025-07-25 00:35:07,327 basehttp 11188 10428 "GET /api/v1/api/users/template/?role=[object+PointerEvent] HTTP/1.1" 404 4107
WARNING 2025-07-25 00:35:10,242 log 11188 3360 Not Found: /api/v1/api/users/template/
WARNING 2025-07-25 00:35:10,258 basehttp 11188 3360 "GET /api/v1/api/users/template/?role=representative HTTP/1.1" 404 4100
WARNING 2025-07-25 00:35:11,897 log 11188 2300 Not Found: /api/v1/api/users/template/
WARNING 2025-07-25 00:35:11,897 basehttp 11188 2300 "GET /api/v1/api/users/template/?role=staff HTTP/1.1" 404 4091
WARNING 2025-07-25 00:36:27,468 log 11188 4732 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:36:27,485 basehttp 11188 4732 "GET /api/v1/api/users/export/?role=[object+PointerEvent] HTTP/1.1" 404 4101
WARNING 2025-07-25 00:36:32,915 log 11188 4072 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:36:32,915 basehttp 11188 4072 "GET /api/v1/api/users/export/ HTTP/1.1" 404 4074
WARNING 2025-07-25 00:36:37,535 log 11188 12732 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:36:37,535 basehttp 11188 12732 "GET /api/v1/api/users/export/?role=representative HTTP/1.1" 404 4094
WARNING 2025-07-25 00:36:40,350 log 11188 1768 Not Found: /api/v1/api/users/export/
WARNING 2025-07-25 00:36:40,350 basehttp 11188 1768 "GET /api/v1/api/users/export/?role=staff HTTP/1.1" 404 4085
INFO 2025-07-25 00:38:14,302 basehttp 11188 4352 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:14,639 basehttp 11188 10464 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:14,644 basehttp 11188 7536 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:38:14,652 basehttp 11188 12344 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:14,966 basehttp 11188 5992 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:38:33,391 basehttp 11188 6780 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:33,658 basehttp 11188 8060 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:33,720 basehttp 11188 14980 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:38:33,720 basehttp 11188 1768 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:33,909 basehttp 11188 10932 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:38:51,917 basehttp 11188 7236 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:52,189 basehttp 11188 14496 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:52,206 basehttp 11188 8780 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:38:52,212 basehttp 11188 12496 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:38:52,278 basehttp 11188 1224 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:41:05,517 autoreload 11188 6104 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-25 00:41:06,170 autoreload 6556 6784 Watching for file changes with StatReloader
INFO 2025-07-25 00:43:02,047 views 6556 14992 工作人员 admin 下载代表导入模板
INFO 2025-07-25 00:43:02,047 basehttp 6556 14992 "GET /api/v1/users/template/?role=representative HTTP/1.1" 200 6729
WARNING 2025-07-25 00:45:46,073 log 6556 13100 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 00:45:46,073 basehttp 6556 13100 "POST /api/v1/users/import/ HTTP/1.1" 400 1219
INFO 2025-07-25 00:47:42,007 autoreload 6556 6784 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-25 00:47:42,731 autoreload 8348 7188 Watching for file changes with StatReloader
INFO 2025-07-25 00:48:08,197 autoreload 8348 7188 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-25 00:48:08,830 autoreload 1072 1768 Watching for file changes with StatReloader
INFO 2025-07-25 00:49:51,388 views 1072 5720 工作人员 admin 下载工作人员导入模板
INFO 2025-07-25 00:49:51,388 basehttp 1072 5720 "GET /api/v1/users/template/?role=staff HTTP/1.1" 200 6429
INFO 2025-07-25 00:51:19,403 signals 1072 5368 新用户创建 - ID: 62, 用户名: staffimport001, 角色: 站点工作人员
INFO 2025-07-25 00:51:19,418 signals 1072 5368 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport001
INFO 2025-07-25 00:51:19,763 signals 1072 5368 新用户创建 - ID: 63, 用户名: staffimport002, 角色: 站点工作人员
INFO 2025-07-25 00:51:19,763 signals 1072 5368 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport002
INFO 2025-07-25 00:51:20,098 signals 1072 5368 新用户创建 - ID: 64, 用户名: staffimport003, 角色: 站点工作人员
INFO 2025-07-25 00:51:20,098 signals 1072 5368 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport003
INFO 2025-07-25 00:51:20,436 signals 1072 5368 新用户创建 - ID: 65, 用户名: staffimport004, 角色: 站点工作人员
INFO 2025-07-25 00:51:20,436 signals 1072 5368 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport004
INFO 2025-07-25 00:51:20,768 signals 1072 5368 新用户创建 - ID: 66, 用户名: staffimport005, 角色: 站点工作人员
INFO 2025-07-25 00:51:20,768 signals 1072 5368 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport005
INFO 2025-07-25 00:51:21,130 signals 1072 5368 新用户创建 - ID: 67, 用户名: staffimport006, 角色: 站点工作人员
INFO 2025-07-25 00:51:21,130 signals 1072 5368 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport006
INFO 2025-07-25 00:51:21,130 views 1072 5368 工作人员 admin 批量导入用户，成功：6，失败：0
INFO 2025-07-25 00:51:21,130 basehttp 1072 5368 "POST /api/v1/users/import/ HTTP/1.1" 200 132
INFO 2025-07-25 00:51:21,182 basehttp 1072 11544 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7139
INFO 2025-07-25 00:51:31,349 basehttp 1072 14860 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7139
INFO 2025-07-25 00:51:33,664 basehttp 1072 4228 "GET /api/v1/users/manage/?page=1&page_size=20&is_active=true HTTP/1.1" 200 7139
INFO 2025-07-25 00:51:36,491 basehttp 1072 1820 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7139
INFO 2025-07-25 00:51:38,321 basehttp 1072 3216 "GET /api/v1/users/manage/67/ HTTP/1.1" 200 937
INFO 2025-07-25 00:51:48,757 views 1072 8644 用户 admin 登出
INFO 2025-07-25 00:51:48,757 basehttp 1072 8644 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 00:51:53,517 views 1072 11852 用户 staffimport001 登录成功，IP: 127.0.0.1
INFO 2025-07-25 00:51:53,517 basehttp 1072 11852 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1491
INFO 2025-07-25 00:51:53,548 basehttp 1072 12172 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-25 00:51:54,873 basehttp 1072 11396 "GET /api/v1/users/profile/ HTTP/1.1" 200 997
INFO 2025-07-25 00:51:54,911 basehttp 1072 15252 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:51:56,304 basehttp 1072 13336 "GET /api/v1/users/profile/ HTTP/1.1" 200 997
INFO 2025-07-25 00:51:56,304 basehttp 1072 10664 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7169
INFO 2025-07-25 00:51:58,328 basehttp 1072 12660 "GET /api/v1/users/manage/67/ HTTP/1.1" 200 937
WARNING 2025-07-25 00:52:11,955 signals 1072 8624 用户即将删除 - ID: 67, 用户名: staffimport006, 角色: 站点工作人员
INFO 2025-07-25 00:52:11,970 views 1072 8624 工作人员 staffimport001 删除了用户 staffimport006（站点工作人员）
INFO 2025-07-25 00:52:11,970 basehttp 1072 8624 "DELETE /api/v1/users/manage/67/ HTTP/1.1" 200 71
INFO 2025-07-25 00:52:12,009 basehttp 1072 11740 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7118
WARNING 2025-07-25 00:52:13,818 signals 1072 2576 用户即将删除 - ID: 66, 用户名: staffimport005, 角色: 站点工作人员
INFO 2025-07-25 00:52:13,833 views 1072 2576 工作人员 staffimport001 删除了用户 staffimport005（站点工作人员）
INFO 2025-07-25 00:52:13,833 basehttp 1072 2576 "DELETE /api/v1/users/manage/66/ HTTP/1.1" 200 71
INFO 2025-07-25 00:52:13,868 basehttp 1072 12164 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7067
WARNING 2025-07-25 00:52:15,374 signals 1072 15316 用户即将删除 - ID: 65, 用户名: staffimport004, 角色: 站点工作人员
INFO 2025-07-25 00:52:15,374 views 1072 15316 工作人员 staffimport001 删除了用户 staffimport004（站点工作人员）
INFO 2025-07-25 00:52:15,391 basehttp 1072 15316 "DELETE /api/v1/users/manage/65/ HTTP/1.1" 200 71
INFO 2025-07-25 00:52:15,431 basehttp 1072 5256 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7016
WARNING 2025-07-25 00:52:16,918 signals 1072 10048 用户即将删除 - ID: 64, 用户名: staffimport003, 角色: 站点工作人员
INFO 2025-07-25 00:52:16,932 views 1072 10048 工作人员 staffimport001 删除了用户 staffimport003（站点工作人员）
INFO 2025-07-25 00:52:16,932 basehttp 1072 10048 "DELETE /api/v1/users/manage/64/ HTTP/1.1" 200 71
INFO 2025-07-25 00:52:16,999 basehttp 1072 13436 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6965
WARNING 2025-07-25 00:52:20,023 signals 1072 160 用户即将删除 - ID: 63, 用户名: staffimport002, 角色: 站点工作人员
INFO 2025-07-25 00:52:20,023 views 1072 160 工作人员 staffimport001 删除了用户 staffimport002（站点工作人员）
INFO 2025-07-25 00:52:20,023 basehttp 1072 160 "DELETE /api/v1/users/manage/63/ HTTP/1.1" 200 71
INFO 2025-07-25 00:52:20,069 basehttp 1072 3700 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6914
WARNING 2025-07-25 00:52:26,332 log 1072 8308 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 00:52:26,332 basehttp 1072 8308 "POST /api/v1/users/import/ HTTP/1.1" 400 211
INFO 2025-07-25 00:52:35,483 basehttp 1072 11416 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6914
INFO 2025-07-25 00:52:40,531 views 1072 4300 用户 staffimport001 登出
INFO 2025-07-25 00:52:40,531 basehttp 1072 4300 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
WARNING 2025-07-25 00:52:46,473 log 1072 14356 Bad Request: /api/v1/users/auth/login/
WARNING 2025-07-25 00:52:46,473 basehttp 1072 14356 "POST /api/v1/users/auth/login/ HTTP/1.1" 400 101
INFO 2025-07-25 00:52:50,578 views 1072 5296 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 00:52:50,578 basehttp 1072 5296 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 00:52:50,626 basehttp 1072 8744 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-25 00:52:51,909 basehttp 1072 10136 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:52:51,954 basehttp 1072 10568 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:52:53,301 basehttp 1072 6780 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:52:53,317 basehttp 1072 5100 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6914
WARNING 2025-07-25 00:53:01,856 log 1072 13304 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 00:53:01,856 basehttp 1072 13304 "POST /api/v1/users/import/ HTTP/1.1" 400 211
WARNING 2025-07-25 00:53:13,343 signals 1072 11132 用户即将删除 - ID: 62, 用户名: staffimport001, 角色: 站点工作人员
INFO 2025-07-25 00:53:13,358 views 1072 11132 工作人员 admin 删除了用户 staffimport001（站点工作人员）
INFO 2025-07-25 00:53:13,358 basehttp 1072 11132 "DELETE /api/v1/users/manage/62/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:13,388 basehttp 1072 10268 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
INFO 2025-07-25 00:53:18,024 signals 1072 4756 新用户创建 - ID: 68, 用户名: staffimport001, 角色: 站点工作人员
INFO 2025-07-25 00:53:18,024 signals 1072 4756 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport001
INFO 2025-07-25 00:53:18,363 signals 1072 4756 新用户创建 - ID: 69, 用户名: staffimport002, 角色: 站点工作人员
INFO 2025-07-25 00:53:18,363 signals 1072 4756 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport002
INFO 2025-07-25 00:53:18,702 signals 1072 4756 新用户创建 - ID: 70, 用户名: staffimport003, 角色: 站点工作人员
INFO 2025-07-25 00:53:18,702 signals 1072 4756 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport003
INFO 2025-07-25 00:53:19,046 signals 1072 4756 新用户创建 - ID: 71, 用户名: staffimport004, 角色: 站点工作人员
INFO 2025-07-25 00:53:19,046 signals 1072 4756 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport004
INFO 2025-07-25 00:53:19,383 signals 1072 4756 新用户创建 - ID: 72, 用户名: staffimport005, 角色: 站点工作人员
INFO 2025-07-25 00:53:19,383 signals 1072 4756 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport005
INFO 2025-07-25 00:53:19,717 signals 1072 4756 新用户创建 - ID: 73, 用户名: staffimport006, 角色: 站点工作人员
INFO 2025-07-25 00:53:19,717 signals 1072 4756 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport006
INFO 2025-07-25 00:53:19,717 views 1072 4756 工作人员 admin 批量导入用户，成功：6，失败：0
INFO 2025-07-25 00:53:19,717 basehttp 1072 4756 "POST /api/v1/users/import/ HTTP/1.1" 200 132
INFO 2025-07-25 00:53:19,753 basehttp 1072 14484 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7139
WARNING 2025-07-25 00:53:28,393 signals 1072 5164 用户即将删除 - ID: 73, 用户名: staffimport006, 角色: 站点工作人员
INFO 2025-07-25 00:53:28,393 views 1072 5164 工作人员 admin 删除了用户 staffimport006（站点工作人员）
INFO 2025-07-25 00:53:28,393 basehttp 1072 5164 "DELETE /api/v1/users/manage/73/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:28,435 basehttp 1072 2360 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7088
WARNING 2025-07-25 00:53:29,984 signals 1072 10780 用户即将删除 - ID: 72, 用户名: staffimport005, 角色: 站点工作人员
INFO 2025-07-25 00:53:29,984 views 1072 10780 工作人员 admin 删除了用户 staffimport005（站点工作人员）
INFO 2025-07-25 00:53:29,984 basehttp 1072 10780 "DELETE /api/v1/users/manage/72/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:30,022 basehttp 1072 13136 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7037
WARNING 2025-07-25 00:53:31,552 signals 1072 1156 用户即将删除 - ID: 71, 用户名: staffimport004, 角色: 站点工作人员
INFO 2025-07-25 00:53:31,560 views 1072 1156 工作人员 admin 删除了用户 staffimport004（站点工作人员）
INFO 2025-07-25 00:53:31,560 basehttp 1072 1156 "DELETE /api/v1/users/manage/71/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:31,595 basehttp 1072 10628 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6986
WARNING 2025-07-25 00:53:32,995 signals 1072 11312 用户即将删除 - ID: 70, 用户名: staffimport003, 角色: 站点工作人员
INFO 2025-07-25 00:53:32,995 views 1072 11312 工作人员 admin 删除了用户 staffimport003（站点工作人员）
INFO 2025-07-25 00:53:32,995 basehttp 1072 11312 "DELETE /api/v1/users/manage/70/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:33,042 basehttp 1072 2268 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6935
WARNING 2025-07-25 00:53:34,804 signals 1072 11808 用户即将删除 - ID: 69, 用户名: staffimport002, 角色: 站点工作人员
INFO 2025-07-25 00:53:34,804 views 1072 11808 工作人员 admin 删除了用户 staffimport002（站点工作人员）
INFO 2025-07-25 00:53:34,804 basehttp 1072 11808 "DELETE /api/v1/users/manage/69/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:34,852 basehttp 1072 6732 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6884
WARNING 2025-07-25 00:53:36,299 signals 1072 5908 用户即将删除 - ID: 68, 用户名: staffimport001, 角色: 站点工作人员
INFO 2025-07-25 00:53:36,315 views 1072 5908 工作人员 admin 删除了用户 staffimport001（站点工作人员）
INFO 2025-07-25 00:53:36,315 basehttp 1072 5908 "DELETE /api/v1/users/manage/68/ HTTP/1.1" 200 71
INFO 2025-07-25 00:53:36,359 basehttp 1072 5004 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6833
WARNING 2025-07-25 00:53:37,916 signals 1072 13808 用户即将删除 - ID: 61, 用户名: testuser, 角色: 人大代表
INFO 2025-07-25 00:53:37,916 views 1072 13808 工作人员 admin 删除了用户 testuser（人大代表）
INFO 2025-07-25 00:53:37,916 basehttp 1072 13808 "DELETE /api/v1/users/manage/61/ HTTP/1.1" 200 65
INFO 2025-07-25 00:53:37,965 basehttp 1072 11104 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6824
WARNING 2025-07-25 00:53:40,307 signals 1072 9200 用户即将删除 - ID: 60, 用户名: user01, 角色: 人大代表
INFO 2025-07-25 00:53:40,317 views 1072 9200 工作人员 admin 删除了用户 user01（人大代表）
INFO 2025-07-25 00:53:40,317 basehttp 1072 9200 "DELETE /api/v1/users/manage/60/ HTTP/1.1" 200 63
INFO 2025-07-25 00:53:40,351 basehttp 1072 6664 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6787
INFO 2025-07-25 00:53:47,143 views 1072 8848 工作人员 admin 下载代表导入模板
INFO 2025-07-25 00:53:47,143 basehttp 1072 8848 "GET /api/v1/users/template/?role=representative HTTP/1.1" 200 6729
INFO 2025-07-25 00:55:00,354 signals 1072 14980 新用户创建 - ID: 74, 用户名: repimport001, 角色: 人大代表
INFO 2025-07-25 00:55:00,354 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport001
INFO 2025-07-25 00:55:00,700 signals 1072 14980 新用户创建 - ID: 75, 用户名: repimport002, 角色: 人大代表
INFO 2025-07-25 00:55:00,700 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport002
INFO 2025-07-25 00:55:01,037 signals 1072 14980 新用户创建 - ID: 76, 用户名: repimport003, 角色: 人大代表
INFO 2025-07-25 00:55:01,046 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport003
INFO 2025-07-25 00:55:01,395 signals 1072 14980 新用户创建 - ID: 77, 用户名: repimport004, 角色: 人大代表
INFO 2025-07-25 00:55:01,395 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport004
INFO 2025-07-25 00:55:01,718 signals 1072 14980 新用户创建 - ID: 78, 用户名: repimport005, 角色: 人大代表
INFO 2025-07-25 00:55:01,718 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport005
INFO 2025-07-25 00:55:02,063 signals 1072 14980 新用户创建 - ID: 79, 用户名: repimport006, 角色: 人大代表
INFO 2025-07-25 00:55:02,063 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport006
INFO 2025-07-25 00:55:02,400 signals 1072 14980 新用户创建 - ID: 80, 用户名: repimport007, 角色: 人大代表
INFO 2025-07-25 00:55:02,400 signals 1072 14980 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport007
INFO 2025-07-25 00:55:02,400 views 1072 14980 工作人员 admin 批量导入用户，成功：7，失败：0
INFO 2025-07-25 00:55:02,400 basehttp 1072 14980 "POST /api/v1/users/import/ HTTP/1.1" 200 132
INFO 2025-07-25 00:55:02,441 basehttp 1072 13580 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6857
INFO 2025-07-25 00:55:14,532 signals 1072 2344 新用户创建 - ID: 81, 用户名: staffimport001, 角色: 站点工作人员
INFO 2025-07-25 00:55:14,532 signals 1072 2344 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport001
INFO 2025-07-25 00:55:14,881 signals 1072 2344 新用户创建 - ID: 82, 用户名: staffimport002, 角色: 站点工作人员
INFO 2025-07-25 00:55:14,881 signals 1072 2344 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport002
INFO 2025-07-25 00:55:15,217 signals 1072 2344 新用户创建 - ID: 83, 用户名: staffimport003, 角色: 站点工作人员
INFO 2025-07-25 00:55:15,217 signals 1072 2344 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport003
INFO 2025-07-25 00:55:15,554 signals 1072 2344 新用户创建 - ID: 84, 用户名: staffimport004, 角色: 站点工作人员
INFO 2025-07-25 00:55:15,554 signals 1072 2344 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport004
INFO 2025-07-25 00:55:15,886 signals 1072 2344 新用户创建 - ID: 85, 用户名: staffimport005, 角色: 站点工作人员
INFO 2025-07-25 00:55:15,886 signals 1072 2344 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport005
INFO 2025-07-25 00:55:16,228 signals 1072 2344 新用户创建 - ID: 86, 用户名: staffimport006, 角色: 站点工作人员
INFO 2025-07-25 00:55:16,228 signals 1072 2344 工作人员信息创建 - 姓名: 李四, 职位: 联络员, 站点: 江南区人大代表联络站, 关联用户: staffimport006
INFO 2025-07-25 00:55:16,228 views 1072 2344 工作人员 admin 批量导入用户，成功：6，失败：0
INFO 2025-07-25 00:55:16,228 basehttp 1072 2344 "POST /api/v1/users/import/ HTTP/1.1" 200 132
INFO 2025-07-25 00:55:16,265 basehttp 1072 7624 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7163
INFO 2025-07-25 00:55:30,751 views 1072 4952 用户 admin 登出
INFO 2025-07-25 00:55:30,751 basehttp 1072 4952 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 00:55:43,070 views 1072 13768 用户 repimport001 登录成功，IP: 127.0.0.1
INFO 2025-07-25 00:55:43,070 basehttp 1072 13768 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1708
INFO 2025-07-25 00:55:43,186 basehttp 1072 7520 "GET /api/v1/users/profile/ HTTP/1.1" 200 1214
INFO 2025-07-25 00:55:43,231 basehttp 1072 6440 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-25 00:55:43,240 basehttp 1072 1660 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 00:55:45,828 views 1072 7544 用户 repimport001 登出
INFO 2025-07-25 00:55:45,828 basehttp 1072 7544 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 00:56:01,173 views 1072 10932 用户 repimport005 登录成功，IP: 127.0.0.1
INFO 2025-07-25 00:56:01,173 basehttp 1072 10932 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1708
INFO 2025-07-25 00:56:01,269 basehttp 1072 7044 "GET /api/v1/users/profile/ HTTP/1.1" 200 1214
INFO 2025-07-25 00:56:01,313 basehttp 1072 336 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-25 00:56:01,313 basehttp 1072 12732 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 00:56:08,774 basehttp 1072 14180 "GET /api/v1/users/profile/ HTTP/1.1" 200 1214
INFO 2025-07-25 00:56:24,027 views 1072 1764 用户 repimport005 登出
INFO 2025-07-25 00:56:24,027 basehttp 1072 1764 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 00:56:28,162 views 1072 6784 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 00:56:28,162 basehttp 1072 6784 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 00:56:28,208 basehttp 1072 9548 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-25 00:56:29,363 basehttp 1072 13932 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:56:29,403 basehttp 1072 2352 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 00:56:30,506 basehttp 1072 5832 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 00:56:30,521 basehttp 1072 13196 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7223
WARNING 2025-07-25 00:56:33,065 signals 1072 6032 用户即将删除 - ID: 86, 用户名: staffimport006, 角色: 站点工作人员
INFO 2025-07-25 00:56:33,065 views 1072 6032 工作人员 admin 删除了用户 staffimport006（站点工作人员）
INFO 2025-07-25 00:56:33,065 basehttp 1072 6032 "DELETE /api/v1/users/manage/86/ HTTP/1.1" 200 71
INFO 2025-07-25 00:56:33,123 basehttp 1072 6152 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7172
WARNING 2025-07-25 00:56:35,106 signals 1072 11232 用户即将删除 - ID: 85, 用户名: staffimport005, 角色: 站点工作人员
INFO 2025-07-25 00:56:35,106 views 1072 11232 工作人员 admin 删除了用户 staffimport005（站点工作人员）
INFO 2025-07-25 00:56:35,106 basehttp 1072 11232 "DELETE /api/v1/users/manage/85/ HTTP/1.1" 200 71
INFO 2025-07-25 00:56:35,149 basehttp 1072 10864 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7121
WARNING 2025-07-25 00:56:37,199 signals 1072 9696 用户即将删除 - ID: 84, 用户名: staffimport004, 角色: 站点工作人员
INFO 2025-07-25 00:56:37,199 views 1072 9696 工作人员 admin 删除了用户 staffimport004（站点工作人员）
INFO 2025-07-25 00:56:37,199 basehttp 1072 9696 "DELETE /api/v1/users/manage/84/ HTTP/1.1" 200 71
INFO 2025-07-25 00:56:37,247 basehttp 1072 7008 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7070
WARNING 2025-07-25 00:56:38,704 signals 1072 13192 用户即将删除 - ID: 83, 用户名: staffimport003, 角色: 站点工作人员
INFO 2025-07-25 00:56:38,719 views 1072 13192 工作人员 admin 删除了用户 staffimport003（站点工作人员）
INFO 2025-07-25 00:56:38,721 basehttp 1072 13192 "DELETE /api/v1/users/manage/83/ HTTP/1.1" 200 71
INFO 2025-07-25 00:56:38,750 basehttp 1072 2520 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7019
WARNING 2025-07-25 00:56:40,768 signals 1072 6852 用户即将删除 - ID: 82, 用户名: staffimport002, 角色: 站点工作人员
INFO 2025-07-25 00:56:40,768 views 1072 6852 工作人员 admin 删除了用户 staffimport002（站点工作人员）
INFO 2025-07-25 00:56:40,768 basehttp 1072 6852 "DELETE /api/v1/users/manage/82/ HTTP/1.1" 200 71
INFO 2025-07-25 00:56:40,806 basehttp 1072 12020 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6968
WARNING 2025-07-25 00:56:42,418 signals 1072 3312 用户即将删除 - ID: 81, 用户名: staffimport001, 角色: 站点工作人员
INFO 2025-07-25 00:56:42,418 views 1072 3312 工作人员 admin 删除了用户 staffimport001（站点工作人员）
INFO 2025-07-25 00:56:42,418 basehttp 1072 3312 "DELETE /api/v1/users/manage/81/ HTTP/1.1" 200 71
INFO 2025-07-25 00:56:42,456 basehttp 1072 11152 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6917
WARNING 2025-07-25 00:56:43,993 signals 1072 13528 用户即将删除 - ID: 80, 用户名: repimport007, 角色: 人大代表
INFO 2025-07-25 00:56:44,009 views 1072 13528 工作人员 admin 删除了用户 repimport007（人大代表）
INFO 2025-07-25 00:56:44,009 basehttp 1072 13528 "DELETE /api/v1/users/manage/80/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:44,063 basehttp 1072 15156 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6907
WARNING 2025-07-25 00:56:45,786 signals 1072 8588 用户即将删除 - ID: 79, 用户名: repimport006, 角色: 人大代表
INFO 2025-07-25 00:56:45,803 views 1072 8588 工作人员 admin 删除了用户 repimport006（人大代表）
INFO 2025-07-25 00:56:45,803 basehttp 1072 8588 "DELETE /api/v1/users/manage/79/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:45,846 basehttp 1072 6132 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6897
WARNING 2025-07-25 00:56:47,487 signals 1072 3220 用户即将删除 - ID: 78, 用户名: repimport005, 角色: 人大代表
INFO 2025-07-25 00:56:47,487 views 1072 3220 工作人员 admin 删除了用户 repimport005（人大代表）
INFO 2025-07-25 00:56:47,487 basehttp 1072 3220 "DELETE /api/v1/users/manage/78/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:47,526 basehttp 1072 3832 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6857
WARNING 2025-07-25 00:56:48,725 signals 1072 11336 用户即将删除 - ID: 77, 用户名: repimport004, 角色: 人大代表
INFO 2025-07-25 00:56:48,740 views 1072 11336 工作人员 admin 删除了用户 repimport004（人大代表）
INFO 2025-07-25 00:56:48,740 basehttp 1072 11336 "DELETE /api/v1/users/manage/77/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:48,772 basehttp 1072 272 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6847
WARNING 2025-07-25 00:56:50,285 signals 1072 14632 用户即将删除 - ID: 76, 用户名: repimport003, 角色: 人大代表
INFO 2025-07-25 00:56:50,298 views 1072 14632 工作人员 admin 删除了用户 repimport003（人大代表）
INFO 2025-07-25 00:56:50,298 basehttp 1072 14632 "DELETE /api/v1/users/manage/76/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:50,332 basehttp 1072 5324 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6837
WARNING 2025-07-25 00:56:51,826 signals 1072 1592 用户即将删除 - ID: 75, 用户名: repimport002, 角色: 人大代表
INFO 2025-07-25 00:56:51,826 views 1072 1592 工作人员 admin 删除了用户 repimport002（人大代表）
INFO 2025-07-25 00:56:51,826 basehttp 1072 1592 "DELETE /api/v1/users/manage/75/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:51,876 basehttp 1072 5768 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6827
WARNING 2025-07-25 00:56:53,214 signals 1072 3364 用户即将删除 - ID: 74, 用户名: repimport001, 角色: 人大代表
INFO 2025-07-25 00:56:53,214 views 1072 3364 工作人员 admin 删除了用户 repimport001（人大代表）
INFO 2025-07-25 00:56:53,214 basehttp 1072 3364 "DELETE /api/v1/users/manage/74/ HTTP/1.1" 200 69
INFO 2025-07-25 00:56:53,250 basehttp 1072 996 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6787
WARNING 2025-07-25 00:56:56,472 signals 1072 13456 用户即将删除 - ID: 57, 用户名: test_representative, 角色: 人大代表
INFO 2025-07-25 00:56:56,488 views 1072 13456 工作人员 admin 删除了用户 test_representative（人大代表）
INFO 2025-07-25 00:56:56,488 basehttp 1072 13456 "DELETE /api/v1/users/manage/57/ HTTP/1.1" 200 76
INFO 2025-07-25 00:56:56,525 basehttp 1072 13184 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 00:57:01,214 views 1072 13372 工作人员 admin 导出用户列表，筛选条件：role=None, is_active=None
INFO 2025-07-25 00:57:01,214 basehttp 1072 13372 "GET /api/v1/users/export/ HTTP/1.1" 200 10754
INFO 2025-07-25 00:58:52,155 autoreload 1072 1768 C:\code\NPC\backend\api\users\urls.py changed, reloading.
INFO 2025-07-25 00:58:52,817 autoreload 14904 6296 Watching for file changes with StatReloader
INFO 2025-07-25 00:59:30,981 basehttp 14904 5856 "GET /api/v1/users/manage/?page=1&page_size=20&role=representative HTTP/1.1" 200 6781
INFO 2025-07-25 00:59:33,237 basehttp 14904 8452 "GET /api/v1/users/manage/?page=1&page_size=20&role=representative&is_active=true HTTP/1.1" 200 6781
INFO 2025-07-25 00:59:34,778 basehttp 14904 11576 "GET /api/v1/users/manage/?page=1&page_size=20&role=representative&is_active=false HTTP/1.1" 200 109
INFO 2025-07-25 00:59:36,137 basehttp 14904 14840 "GET /api/v1/users/manage/?page=1&page_size=20&role=representative HTTP/1.1" 200 6781
INFO 2025-07-25 00:59:39,236 basehttp 14904 10248 "GET /api/v1/users/manage/53/ HTTP/1.1" 200 1138
INFO 2025-07-25 01:02:05,583 views 14904 13932 用户 admin 登出
INFO 2025-07-25 01:02:05,583 basehttp 14904 13932 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 01:02:10,416 views 14904 14924 用户 rep001 登录成功，IP: 127.0.0.1
INFO 2025-07-25 01:02:10,431 basehttp 14904 14924 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 352405
INFO 2025-07-25 01:02:10,519 basehttp 14904 5944 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:02:10,572 basehttp 14904 9696 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 01:02:10,589 basehttp 14904 2724 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 01:02:12,466 basehttp 14904 10864 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 01:02:12,466 views 14904 12072 用户 rep001 查询履职记录列表
INFO 2025-07-25 01:02:12,466 basehttp 14904 8772 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 01:02:12,477 basehttp 14904 12072 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 01:02:25,025 views 14904 14784 用户 rep001 查看意见建议列表，返回 10 条记录
INFO 2025-07-25 01:02:25,089 basehttp 14904 14784 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 31879
INFO 2025-07-25 01:02:26,077 basehttp 14904 12020 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 01:02:26,087 views 14904 14548 用户 rep001 查询履职记录列表
INFO 2025-07-25 01:02:26,091 basehttp 14904 14548 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 01:02:26,091 basehttp 14904 15216 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 01:02:31,101 basehttp 14904 14632 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:02:31,920 basehttp 14904 9156 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364232
INFO 2025-07-25 01:02:53,696 views 14904 6764 用户 rep001 查看意见建议列表，返回 10 条记录
INFO 2025-07-25 01:02:53,757 basehttp 14904 6764 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 31879
INFO 2025-07-25 01:02:59,616 basehttp 14904 10792 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 01:02:59,616 basehttp 14904 14600 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 01:02:59,628 views 14904 4204 用户 rep001 查询履职记录列表
INFO 2025-07-25 01:02:59,634 basehttp 14904 4204 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 01:03:14,373 views 14904 336 获取AI总结详情成功：22
INFO 2025-07-25 01:03:14,373 basehttp 14904 336 "GET /api/v1/ai-summaries/2025/ HTTP/1.1" 200 352541
WARNING 2025-07-25 01:03:14,403 views 14904 7992 AI总结不存在，代表：李代表，年份：2024
WARNING 2025-07-25 01:03:14,406 log 14904 7992 Not Found: /api/v1/ai-summaries/2024/
WARNING 2025-07-25 01:03:14,407 basehttp 14904 7992 "GET /api/v1/ai-summaries/2024/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:14,419 views 14904 8916 AI总结不存在，代表：李代表，年份：2023
WARNING 2025-07-25 01:03:14,419 log 14904 8916 Not Found: /api/v1/ai-summaries/2023/
WARNING 2025-07-25 01:03:14,425 basehttp 14904 8916 "GET /api/v1/ai-summaries/2023/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:14,442 views 14904 13424 AI总结不存在，代表：李代表，年份：2022
WARNING 2025-07-25 01:03:14,442 log 14904 13424 Not Found: /api/v1/ai-summaries/2022/
WARNING 2025-07-25 01:03:14,444 basehttp 14904 13424 "GET /api/v1/ai-summaries/2022/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:14,459 views 14904 6200 AI总结不存在，代表：李代表，年份：2021
WARNING 2025-07-25 01:03:14,459 log 14904 6200 Not Found: /api/v1/ai-summaries/2021/
WARNING 2025-07-25 01:03:14,464 basehttp 14904 6200 "GET /api/v1/ai-summaries/2021/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:14,483 views 14904 5968 AI总结不存在，代表：李代表，年份：2020
WARNING 2025-07-25 01:03:14,483 log 14904 5968 Not Found: /api/v1/ai-summaries/2020/
WARNING 2025-07-25 01:03:14,487 basehttp 14904 5968 "GET /api/v1/ai-summaries/2020/ HTTP/1.1" 404 88
INFO 2025-07-25 01:03:15,534 views 14904 4840 用户 rep001 查看意见建议列表，返回 10 条记录
INFO 2025-07-25 01:03:15,611 basehttp 14904 4840 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 31879
INFO 2025-07-25 01:03:17,930 views 14904 11000 获取AI总结详情成功：22
INFO 2025-07-25 01:03:17,941 basehttp 14904 11000 "GET /api/v1/ai-summaries/2025/ HTTP/1.1" 200 352541
WARNING 2025-07-25 01:03:17,952 views 14904 888 AI总结不存在，代表：李代表，年份：2024
WARNING 2025-07-25 01:03:17,963 log 14904 888 Not Found: /api/v1/ai-summaries/2024/
WARNING 2025-07-25 01:03:17,963 basehttp 14904 888 "GET /api/v1/ai-summaries/2024/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:17,977 views 14904 9820 AI总结不存在，代表：李代表，年份：2023
WARNING 2025-07-25 01:03:17,983 log 14904 9820 Not Found: /api/v1/ai-summaries/2023/
WARNING 2025-07-25 01:03:17,984 basehttp 14904 9820 "GET /api/v1/ai-summaries/2023/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:17,997 views 14904 3880 AI总结不存在，代表：李代表，年份：2022
WARNING 2025-07-25 01:03:18,003 log 14904 3880 Not Found: /api/v1/ai-summaries/2022/
WARNING 2025-07-25 01:03:18,003 basehttp 14904 3880 "GET /api/v1/ai-summaries/2022/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:18,032 views 14904 15092 AI总结不存在，代表：李代表，年份：2021
WARNING 2025-07-25 01:03:18,032 log 14904 15092 Not Found: /api/v1/ai-summaries/2021/
WARNING 2025-07-25 01:03:18,034 basehttp 14904 15092 "GET /api/v1/ai-summaries/2021/ HTTP/1.1" 404 88
WARNING 2025-07-25 01:03:18,055 views 14904 14528 AI总结不存在，代表：李代表，年份：2020
WARNING 2025-07-25 01:03:18,055 log 14904 14528 Not Found: /api/v1/ai-summaries/2020/
WARNING 2025-07-25 01:03:18,056 basehttp 14904 14528 "GET /api/v1/ai-summaries/2020/ HTTP/1.1" 404 88
INFO 2025-07-25 01:03:26,638 basehttp 14904 3056 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 01:03:26,638 views 14904 13888 用户 rep001 查询履职记录列表
INFO 2025-07-25 01:03:26,638 basehttp 14904 15144 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 01:03:26,638 basehttp 14904 13888 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 01:04:06,240 basehttp 14904 1216 "GET /api/v1/bigscreen/district-members/?district=%E6%B2%9B%E9%B8%BF%E7%89%87%E5%8C%BA HTTP/1.1" 200 61
INFO 2025-07-25 01:04:08,290 basehttp 14904 11504 "GET /api/v1/bigscreen/district-members/?district=%E9%82%A3%E5%8E%86%E7%89%87%E5%8C%BA HTTP/1.1" 200 350997
INFO 2025-07-25 01:04:28,598 views 14904 13328 用户 rep001 登出
INFO 2025-07-25 01:04:28,598 basehttp 14904 13328 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 01:04:32,978 views 14904 14764 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 01:04:32,978 basehttp 14904 14764 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 01:04:33,029 basehttp 14904 12376 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-25 01:04:34,350 basehttp 14904 3876 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:04:34,384 basehttp 14904 4964 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:04:35,468 basehttp 14904 5348 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:04:35,477 basehttp 14904 1548 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
WARNING 2025-07-25 01:05:20,709 log 14904 7076 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:05:20,709 basehttp 14904 7076 "POST /api/v1/users/import/ HTTP/1.1" 400 483
INFO 2025-07-25 01:07:44,506 views 14904 13992 用户 admin 登出
INFO 2025-07-25 01:07:44,506 basehttp 14904 13992 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 01:07:49,584 views 14904 4236 用户 rep001 登录成功，IP: 127.0.0.1
INFO 2025-07-25 01:07:49,596 basehttp 14904 4236 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 352405
INFO 2025-07-25 01:07:49,678 basehttp 14904 12464 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:07:49,732 basehttp 14904 1552 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 01:07:49,742 basehttp 14904 2268 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 01:07:50,670 basehttp 14904 1352 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:13:51,974 autoreload 14904 6296 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-25 01:13:52,853 autoreload 11304 7392 Watching for file changes with StatReloader
INFO 2025-07-25 01:17:03,070 views 11304 10476 用户 rep001 登出
INFO 2025-07-25 01:17:03,070 basehttp 11304 10476 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 01:17:08,554 views 11304 14356 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 01:17:08,564 basehttp 11304 14356 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 01:17:08,606 basehttp 11304 7236 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-25 01:17:09,753 basehttp 11304 4964 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:17:09,803 basehttp 11304 6076 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:17:10,940 basehttp 11304 12448 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:17:10,960 basehttp 11304 10204 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
WARNING 2025-07-25 01:17:30,341 log 11304 14884 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:17:30,341 basehttp 11304 14884 "POST /api/v1/users/import/ HTTP/1.1" 400 793
INFO 2025-07-25 01:17:41,207 views 11304 912 工作人员 admin 下载代表导入模板
INFO 2025-07-25 01:17:41,207 basehttp 11304 912 "GET /api/v1/users/template/?role=representative HTTP/1.1" 200 6924
INFO 2025-07-25 01:17:43,750 views 11304 12036 工作人员 admin 下载工作人员导入模板
INFO 2025-07-25 01:17:43,750 basehttp 11304 12036 "GET /api/v1/users/template/?role=staff HTTP/1.1" 200 6429
INFO 2025-07-25 01:17:55,025 views 11304 10996 工作人员 admin 下载代表导入模板
INFO 2025-07-25 01:17:55,025 basehttp 11304 10996 "GET /api/v1/users/template/?role=representative HTTP/1.1" 200 6924
INFO 2025-07-25 01:17:58,209 views 11304 13808 工作人员 admin 下载工作人员导入模板
INFO 2025-07-25 01:17:58,209 basehttp 11304 13808 "GET /api/v1/users/template/?role=staff HTTP/1.1" 200 6429
WARNING 2025-07-25 01:18:02,215 log 11304 6032 Bad Request: /api/v1/users/template/
WARNING 2025-07-25 01:18:02,215 basehttp 11304 6032 "GET /api/v1/users/template/?role=[object+PointerEvent] HTTP/1.1" 400 78
WARNING 2025-07-25 01:19:47,627 log 11304 7316 Bad Request: /api/v1/users/template/
WARNING 2025-07-25 01:19:47,627 basehttp 11304 7316 "GET /api/v1/users/template/?role=[object+PointerEvent] HTTP/1.1" 400 78
WARNING 2025-07-25 01:19:49,813 log 11304 14752 Unauthorized: /api/v1/users/template/
WARNING 2025-07-25 01:19:49,813 basehttp 11304 14752 "GET /api/v1/users/template/?role=[object+PointerEvent] HTTP/1.1" 401 43
INFO 2025-07-25 01:21:17,499 views 11304 1268 工作人员 admin 下载代表导入模板
INFO 2025-07-25 01:21:17,500 basehttp 11304 1268 "GET /api/v1/users/template/?role=representative HTTP/1.1" 200 6924
INFO 2025-07-25 01:21:19,174 views 11304 492 工作人员 admin 下载工作人员导入模板
INFO 2025-07-25 01:21:19,175 basehttp 11304 492 "GET /api/v1/users/template/?role=staff HTTP/1.1" 200 6429
INFO 2025-07-25 01:21:36,654 basehttp 11304 13684 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:21:36,881 basehttp 11304 14820 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:21:36,893 basehttp 11304 12556 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:21:36,899 basehttp 11304 3220 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 01:21:37,149 basehttp 11304 1236 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:22:00,149 basehttp 11304 7520 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:00,352 basehttp 11304 6916 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:00,384 basehttp 11304 2648 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 01:22:00,384 basehttp 11304 5856 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:00,608 basehttp 11304 12000 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:22:13,189 basehttp 11304 14852 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:13,406 basehttp 11304 1464 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:13,460 basehttp 11304 7916 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 01:22:13,510 basehttp 11304 12416 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:13,777 basehttp 11304 14808 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:22:48,349 basehttp 11304 1408 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:48,556 basehttp 11304 13468 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:48,579 basehttp 11304 14836 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 01:22:48,579 basehttp 11304 14176 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:22:48,836 basehttp 11304 11068 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:24:17,251 views 11304 5056 工作人员 admin 下载代表导入模板
INFO 2025-07-25 01:24:17,267 basehttp 11304 5056 "GET /api/v1/users/template/?role=representative HTTP/1.1" 200 6924
INFO 2025-07-25 01:24:20,174 views 11304 5272 工作人员 admin 下载工作人员导入模板
INFO 2025-07-25 01:24:20,174 basehttp 11304 5272 "GET /api/v1/users/template/?role=staff HTTP/1.1" 200 6429
WARNING 2025-07-25 01:24:35,129 log 11304 5940 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:24:35,148 basehttp 11304 5940 "POST /api/v1/users/import/ HTTP/1.1" 400 793
ERROR 2025-07-25 01:25:20,994 views 11304 13640 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:25:20,997 log 11304 13640 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:25:20,997 basehttp 11304 13640 "POST /api/v1/users/import/ HTTP/1.1" 400 67
ERROR 2025-07-25 01:25:26,116 views 11304 12176 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:25:26,116 log 11304 12176 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:25:26,116 basehttp 11304 12176 "POST /api/v1/users/import/ HTTP/1.1" 400 67
ERROR 2025-07-25 01:25:35,114 views 11304 13768 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:25:35,114 log 11304 13768 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:25:35,114 basehttp 11304 13768 "POST /api/v1/users/import/ HTTP/1.1" 400 67
ERROR 2025-07-25 01:26:30,345 views 11304 5632 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:26:30,363 log 11304 5632 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:26:30,363 basehttp 11304 5632 "POST /api/v1/users/import/ HTTP/1.1" 400 67
ERROR 2025-07-25 01:26:50,721 views 11304 13336 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:26:50,721 log 11304 13336 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:26:50,721 basehttp 11304 13336 "POST /api/v1/users/import/ HTTP/1.1" 400 67
ERROR 2025-07-25 01:27:39,250 views 11304 12516 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:27:39,250 log 11304 12516 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:27:39,250 basehttp 11304 12516 "POST /api/v1/users/import/ HTTP/1.1" 400 67
ERROR 2025-07-25 01:28:27,611 views 11304 13092 导入用户失败: File is not a zip file
WARNING 2025-07-25 01:28:27,611 log 11304 13092 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:28:27,611 basehttp 11304 13092 "POST /api/v1/users/import/ HTTP/1.1" 400 67
WARNING 2025-07-25 01:28:52,078 log 11304 14860 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:28:52,084 basehttp 11304 14860 "POST /api/v1/users/import/ HTTP/1.1" 400 638
INFO 2025-07-25 01:29:28,631 signals 11304 10488 新用户创建 - ID: 87, 用户名: repimport001, 角色: 人大代表
INFO 2025-07-25 01:29:28,648 signals 11304 10488 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport001
INFO 2025-07-25 01:29:28,988 signals 11304 10488 新用户创建 - ID: 88, 用户名: repimport002, 角色: 人大代表
INFO 2025-07-25 01:29:28,988 signals 11304 10488 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport002
INFO 2025-07-25 01:29:29,313 signals 11304 10488 新用户创建 - ID: 89, 用户名: repimport003, 角色: 人大代表
INFO 2025-07-25 01:29:29,313 signals 11304 10488 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport003
INFO 2025-07-25 01:29:29,655 signals 11304 10488 新用户创建 - ID: 90, 用户名: repimport004, 角色: 人大代表
INFO 2025-07-25 01:29:29,655 signals 11304 10488 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport004
INFO 2025-07-25 01:29:29,973 views 11304 10488 工作人员 admin 批量导入用户，成功：4，失败：1
INFO 2025-07-25 01:29:29,993 basehttp 11304 10488 "POST /api/v1/users/import/ HTTP/1.1" 200 210
INFO 2025-07-25 01:29:30,047 basehttp 11304 10444 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6881
INFO 2025-07-25 01:30:51,033 basehttp 11304 15192 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:30:51,283 basehttp 11304 9992 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:30:51,308 basehttp 11304 2076 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6881
INFO 2025-07-25 01:30:51,308 basehttp 11304 12240 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:30:51,537 basehttp 11304 14836 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:30:55,150 autoreload 11304 7392 C:\code\NPC\backend\api\users\views.py changed, reloading.
INFO 2025-07-25 01:30:55,960 autoreload 5808 14480 Watching for file changes with StatReloader
INFO 2025-07-25 01:31:13,421 autoreload 5808 14480 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-25 01:31:14,131 autoreload 7076 9508 Watching for file changes with StatReloader
INFO 2025-07-25 01:31:41,725 basehttp 7076 12556 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:31:41,837 basehttp 7076 2684 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:31:41,856 basehttp 7076 8304 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:31:41,863 basehttp 7076 11860 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6881
INFO 2025-07-25 01:31:41,902 basehttp 7076 5904 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:31:55,314 basehttp 7076 1756 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:31:55,494 basehttp 7076 14464 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:31:55,508 basehttp 7076 11724 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:31:55,514 basehttp 7076 10088 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6881
INFO 2025-07-25 01:31:55,549 basehttp 7076 14248 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
WARNING 2025-07-25 01:33:06,347 log 7076 3836 Bad Request: /api/v1/users/import/
WARNING 2025-07-25 01:33:06,347 basehttp 7076 3836 "POST /api/v1/users/import/ HTTP/1.1" 400 742
WARNING 2025-07-25 01:34:24,736 signals 7076 15164 用户即将删除 - ID: 90, 用户名: repimport004, 角色: 人大代表
INFO 2025-07-25 01:34:24,748 views 7076 15164 工作人员 admin 删除了用户 repimport004（人大代表）
INFO 2025-07-25 01:34:24,748 basehttp 7076 15164 "DELETE /api/v1/users/manage/90/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:24,793 basehttp 7076 6944 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6871
WARNING 2025-07-25 01:34:26,339 signals 7076 2156 用户即将删除 - ID: 89, 用户名: repimport003, 角色: 人大代表
INFO 2025-07-25 01:34:26,354 views 7076 2156 工作人员 admin 删除了用户 repimport003（人大代表）
INFO 2025-07-25 01:34:26,356 basehttp 7076 2156 "DELETE /api/v1/users/manage/89/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:26,394 basehttp 7076 12416 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6861
WARNING 2025-07-25 01:34:28,418 signals 7076 11324 用户即将删除 - ID: 88, 用户名: repimport002, 角色: 人大代表
INFO 2025-07-25 01:34:28,431 views 7076 11324 工作人员 admin 删除了用户 repimport002（人大代表）
INFO 2025-07-25 01:34:28,431 basehttp 7076 11324 "DELETE /api/v1/users/manage/88/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:28,474 basehttp 7076 6560 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6851
WARNING 2025-07-25 01:34:29,917 signals 7076 11624 用户即将删除 - ID: 87, 用户名: repimport001, 角色: 人大代表
INFO 2025-07-25 01:34:29,917 views 7076 11624 工作人员 admin 删除了用户 repimport001（人大代表）
INFO 2025-07-25 01:34:29,917 basehttp 7076 11624 "DELETE /api/v1/users/manage/87/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:29,951 basehttp 7076 3280 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 01:34:44,080 signals 7076 1208 新用户创建 - ID: 91, 用户名: repimport001, 角色: 人大代表
INFO 2025-07-25 01:34:44,080 signals 7076 1208 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport001
INFO 2025-07-25 01:34:44,445 signals 7076 1208 新用户创建 - ID: 92, 用户名: repimport002, 角色: 人大代表
INFO 2025-07-25 01:34:44,445 signals 7076 1208 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport002
INFO 2025-07-25 01:34:44,784 signals 7076 1208 新用户创建 - ID: 93, 用户名: repimport003, 角色: 人大代表
INFO 2025-07-25 01:34:44,784 signals 7076 1208 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport003
INFO 2025-07-25 01:34:45,123 signals 7076 1208 新用户创建 - ID: 94, 用户名: repimport004, 角色: 人大代表
INFO 2025-07-25 01:34:45,123 signals 7076 1208 人大代表信息创建 - 姓名: 张三, 层级: 县区人大代表, 关联用户: repimport004
INFO 2025-07-25 01:34:45,449 views 7076 1208 工作人员 admin 批量导入用户，成功：4，失败：1
INFO 2025-07-25 01:34:45,449 basehttp 7076 1208 "POST /api/v1/users/import/ HTTP/1.1" 200 257
INFO 2025-07-25 01:34:45,513 basehttp 7076 9652 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6881
WARNING 2025-07-25 01:34:53,285 signals 7076 7020 用户即将删除 - ID: 94, 用户名: repimport004, 角色: 人大代表
INFO 2025-07-25 01:34:53,301 views 7076 7020 工作人员 admin 删除了用户 repimport004（人大代表）
INFO 2025-07-25 01:34:53,301 basehttp 7076 7020 "DELETE /api/v1/users/manage/94/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:53,342 basehttp 7076 11396 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6871
WARNING 2025-07-25 01:34:54,722 signals 7076 15024 用户即将删除 - ID: 93, 用户名: repimport003, 角色: 人大代表
INFO 2025-07-25 01:34:54,739 views 7076 15024 工作人员 admin 删除了用户 repimport003（人大代表）
INFO 2025-07-25 01:34:54,739 basehttp 7076 15024 "DELETE /api/v1/users/manage/93/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:54,779 basehttp 7076 148 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6861
WARNING 2025-07-25 01:34:56,127 signals 7076 9612 用户即将删除 - ID: 92, 用户名: repimport002, 角色: 人大代表
INFO 2025-07-25 01:34:56,143 views 7076 9612 工作人员 admin 删除了用户 repimport002（人大代表）
INFO 2025-07-25 01:34:56,143 basehttp 7076 9612 "DELETE /api/v1/users/manage/92/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:56,181 basehttp 7076 14840 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6851
WARNING 2025-07-25 01:34:57,847 signals 7076 14928 用户即将删除 - ID: 91, 用户名: repimport001, 角色: 人大代表
INFO 2025-07-25 01:34:57,847 views 7076 14928 工作人员 admin 删除了用户 repimport001（人大代表）
INFO 2025-07-25 01:34:57,847 basehttp 7076 14928 "DELETE /api/v1/users/manage/91/ HTTP/1.1" 200 69
INFO 2025-07-25 01:34:57,891 basehttp 7076 5352 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 01:36:13,558 basehttp 7076 15032 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:36:14,272 basehttp 7076 13468 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364232
INFO 2025-07-25 01:36:15,841 basehttp 7076 13268 "GET /api/v1/bigscreen/district-members/?district=%E9%82%A3%E5%8E%86%E7%89%87%E5%8C%BA HTTP/1.1" 200 350997
INFO 2025-07-25 01:36:18,202 basehttp 7076 4636 "GET /api/v1/bigscreen/district-members/?district=%E9%82%A3%E6%B4%AA%E7%89%87%E5%8C%BA HTTP/1.1" 200 5769
INFO 2025-07-25 01:45:16,707 basehttp 7076 9580 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:45:16,786 basehttp 7076 12524 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1195
INFO 2025-07-25 01:45:18,089 basehttp 7076 14504 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 01:45:18,180 basehttp 7076 13760 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1628
INFO 2025-07-25 01:45:20,814 views 7076 7684 用户 admin 登出
INFO 2025-07-25 01:45:20,814 basehttp 7076 7684 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 01:45:26,574 views 7076 14360 用户 rep001 登录成功，IP: 127.0.0.1
INFO 2025-07-25 01:45:26,574 basehttp 7076 14360 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 352405
INFO 2025-07-25 01:45:26,665 basehttp 7076 6764 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:45:26,727 basehttp 7076 11036 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 01:45:26,744 basehttp 7076 15024 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 01:45:28,637 basehttp 7076 9548 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 01:45:28,637 basehttp 7076 11420 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 01:45:28,637 views 7076 848 用户 rep001 查询履职记录列表
INFO 2025-07-25 01:45:28,653 basehttp 7076 848 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 01:45:34,761 views 7076 15092 用户 rep001 查看意见建议列表，返回 10 条记录
INFO 2025-07-25 01:45:34,839 basehttp 7076 15092 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 31879
INFO 2025-07-25 01:45:49,406 services 7076 5804 开始语音转文字，文件: voice_recording.webm, 大小: 133635
INFO 2025-07-25 01:45:49,406 services 7076 5804 发送语音转文字请求到: https://dify.gxaigc.cn/v1/audio-to-text
INFO 2025-07-25 01:45:52,288 services 7076 5804 语音转文字成功，结果长度: 11
INFO 2025-07-25 01:45:52,315 services 7076 5804 开始解析语音内容，文字长度: 11
INFO 2025-07-25 01:45:59,781 services 7076 5804 语音内容解析成功，AI返回长度: 552
INFO 2025-07-25 01:45:59,781 basehttp 7076 5804 "POST /api/v1/aiknowledge/voice-to-opinion/ HTTP/1.1" 200 246
INFO 2025-07-25 01:46:25,107 services 7076 12612 开始语音转文字，文件: voice_recording.webm, 大小: 155089
INFO 2025-07-25 01:46:25,107 services 7076 12612 发送语音转文字请求到: https://dify.gxaigc.cn/v1/audio-to-text
INFO 2025-07-25 01:46:27,190 services 7076 12612 语音转文字成功，结果长度: 27
INFO 2025-07-25 01:46:27,190 services 7076 12612 开始解析语音内容，文字长度: 27
INFO 2025-07-25 01:46:31,631 services 7076 12612 语音内容解析成功，AI返回长度: 589
INFO 2025-07-25 01:46:31,633 basehttp 7076 12612 "POST /api/v1/aiknowledge/voice-to-opinion/ HTTP/1.1" 200 351
INFO 2025-07-25 01:46:36,372 services 7076 3804 开始意见建议AI生成流式响应
INFO 2025-07-25 01:46:36,372 services 7076 3804 inputs: {}
INFO 2025-07-25 01:46:36,372 services 7076 3804 query: 请基于以下原始意见内容，生成一份高质量的意见建议：

原始内容：单宁夜宵摊较多，造成道路严重拥堵请相关部门管控一下。
分类：transportation
补充信息：分类：交通出行，标题：关于管控单宁夜宵摊缓解交通拥堵的建议

请生成规范、专业的意见建议内容。
INFO 2025-07-25 01:46:36,372 services 7076 3804 发送SSE请求到Dify: https://dify.gxaigc.cn/v1/chat-messages
INFO 2025-07-25 01:46:40,600 services 7076 3804 开始处理Dify流式响应
INFO 2025-07-25 01:47:03,238 services 7076 3804 无参考文件数据
INFO 2025-07-25 01:47:03,238 services 7076 3804 消息结束
INFO 2025-07-25 01:47:03,239 basehttp 7076 3804 "POST /api/v1/aiknowledge/opinion/generate/sse/ HTTP/1.1" 200 75836
INFO 2025-07-25 01:47:15,298 views 7076 3736 代表 rep001 创建意见建议，ID: 18，标题: 关于管控单宁夜宵摊缓解交通拥堵的建议
INFO 2025-07-25 01:47:15,325 basehttp 7076 3736 "POST /api/v1/opinions/suggestions/create/ HTTP/1.1" 201 356694
INFO 2025-07-25 01:47:15,363 views 7076 7980 代表 rep001 提交意见建议，ID: 18
INFO 2025-07-25 01:47:15,376 basehttp 7076 7980 "POST /api/v1/opinions/suggestions/18/submit/ HTTP/1.1" 200 357202
INFO 2025-07-25 01:47:15,469 views 7076 2940 用户 rep001 查看意见建议列表，返回 10 条记录
INFO 2025-07-25 01:47:15,553 basehttp 7076 2940 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 32715
INFO 2025-07-25 01:47:17,709 views 7076 5560 用户 rep001 查看意见建议详情，ID: 18
INFO 2025-07-25 01:47:17,730 basehttp 7076 5560 "GET /api/v1/opinions/suggestions/18/ HTTP/1.1" 200 357181
INFO 2025-07-25 01:47:24,409 basehttp 7076 8780 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:47:25,174 basehttp 7076 7080 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364550
INFO 2025-07-25 01:58:05,102 basehttp 7076 13920 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 01:59:06,555 views 7076 508 用户 rep001 登出
INFO 2025-07-25 01:59:06,555 basehttp 7076 508 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 10:55:27,475 autoreload 11092 11096 Watching for file changes with StatReloader
WARNING 2025-07-25 10:55:30,367 log 11092 11208 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-25 10:55:30,367 log 11092 11244 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-25 10:55:30,367 basehttp 11092 11208 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
WARNING 2025-07-25 10:55:30,368 basehttp 11092 11244 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-25 10:55:30,425 basehttp 11092 11260 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 483
INFO 2025-07-25 10:55:30,475 basehttp 11092 828 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 10:55:30,476 basehttp 11092 824 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 10:55:30,548 basehttp 11092 2960 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 10:55:30,566 basehttp 11092 3044 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 10:55:32,334 basehttp 11092 1084 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 10:55:32,334 basehttp 11092 3660 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 10:55:32,336 views 11092 4108 用户 rep001 查询履职记录列表
INFO 2025-07-25 10:55:32,341 basehttp 11092 4108 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
WARNING 2025-07-25 10:57:23,558 log 11092 9632 Bad Request: /api/v1/performance/records/
WARNING 2025-07-25 10:57:23,558 basehttp 11092 9632 "POST /api/v1/performance/records/ HTTP/1.1" 400 97
WARNING 2025-07-25 10:57:33,336 log 11092 10196 Bad Request: /api/v1/performance/records/
WARNING 2025-07-25 10:57:33,336 basehttp 11092 10196 "POST /api/v1/performance/records/ HTTP/1.1" 400 97
WARNING 2025-07-25 10:57:38,656 log 11092 992 Bad Request: /api/v1/performance/records/
WARNING 2025-07-25 10:57:38,656 basehttp 11092 992 "POST /api/v1/performance/records/ HTTP/1.1" 400 97
INFO 2025-07-25 10:57:54,264 views 11092 10560 用户 rep001 创建履职记录: 50
INFO 2025-07-25 10:57:54,280 basehttp 11092 10560 "POST /api/v1/performance/records/ HTTP/1.1" 201 722
INFO 2025-07-25 10:57:54,330 views 11092 3836 用户 rep001 查询履职记录列表
INFO 2025-07-25 10:57:54,340 basehttp 11092 3836 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 581
INFO 2025-07-25 10:58:00,851 views 11092 5612 用户 rep001 删除履职记录: 50
INFO 2025-07-25 10:58:00,851 basehttp 11092 5612 "DELETE /api/v1/performance/records/50/ HTTP/1.1" 204 0
INFO 2025-07-25 10:58:00,873 views 11092 2664 用户 rep001 查询履职记录列表
INFO 2025-07-25 10:58:00,877 basehttp 11092 2664 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
WARNING 2025-07-25 11:10:22,949 log 11092 8220 Bad Request: /api/v1/performance/records/
WARNING 2025-07-25 11:10:22,954 basehttp 11092 8220 "POST /api/v1/performance/records/ HTTP/1.1" 400 97
INFO 2025-07-25 11:12:44,086 autoreload 11092 11096 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:12:45,701 autoreload 3484 10508 Watching for file changes with StatReloader
INFO 2025-07-25 11:13:04,079 autoreload 3484 10508 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:13:04,740 autoreload 8220 12788 Watching for file changes with StatReloader
INFO 2025-07-25 11:13:22,909 autoreload 8220 12788 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:13:23,551 autoreload 1424 14684 Watching for file changes with StatReloader
INFO 2025-07-25 11:13:38,613 autoreload 1424 14684 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:13:39,298 autoreload 13288 13908 Watching for file changes with StatReloader
INFO 2025-07-25 11:14:55,364 views 13288 2864 用户 rep001 创建履职记录: 51
INFO 2025-07-25 11:14:55,397 basehttp 13288 2864 "POST /api/v1/performance/records/ HTTP/1.1" 201 697
INFO 2025-07-25 11:14:55,450 views 13288 12616 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:14:55,462 basehttp 13288 12616 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 578
INFO 2025-07-25 11:15:10,579 views 13288 12276 用户 rep001 删除履职记录: 51
INFO 2025-07-25 11:15:10,579 basehttp 13288 12276 "DELETE /api/v1/performance/records/51/ HTTP/1.1" 204 0
INFO 2025-07-25 11:15:10,718 views 13288 13536 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:15:10,725 basehttp 13288 13536 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 11:20:44,545 autoreload 13288 13908 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:20:45,223 autoreload 14024 6464 Watching for file changes with StatReloader
INFO 2025-07-25 11:21:19,957 autoreload 14024 6464 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:21:20,629 autoreload 7140 14992 Watching for file changes with StatReloader
INFO 2025-07-25 11:21:44,983 autoreload 7140 14992 C:\code\NPC\backend\api\performance\serializers.py changed, reloading.
INFO 2025-07-25 11:21:45,603 autoreload 12512 14876 Watching for file changes with StatReloader
INFO 2025-07-25 11:22:18,998 basehttp 12512 12324 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:22:19,226 basehttp 12512 14748 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 11:22:19,232 views 12512 14392 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:22:19,241 basehttp 12512 14392 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-25 11:22:19,239 basehttp 12512 14004 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 829
INFO 2025-07-25 11:22:19,239 basehttp 12512 2872 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:22:19,344 basehttp 12512 4260 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 11:22:19,368 basehttp 12512 13396 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1670
INFO 2025-07-25 11:27:47,452 views 12512 2600 用户 rep001 创建履职记录: 52
INFO 2025-07-25 11:27:47,452 basehttp 12512 2600 "POST /api/v1/performance/records/ HTTP/1.1" 201 616
INFO 2025-07-25 11:27:47,558 views 12512 3364 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:27:47,566 basehttp 12512 3364 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 524
INFO 2025-07-25 11:29:58,679 views 12512 13428 用户 rep001 登出
INFO 2025-07-25 11:29:58,679 basehttp 12512 13428 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 11:30:03,685 views 12512 13884 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 11:30:03,685 basehttp 12512 13884 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 11:30:03,794 basehttp 12512 6224 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1196
INFO 2025-07-25 11:30:05,151 basehttp 12512 15212 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:30:05,223 basehttp 12512 14392 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1817
INFO 2025-07-25 11:30:06,507 basehttp 12512 14140 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:30:06,533 basehttp 12512 6112 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 11:36:41,207 autoreload 12512 14876 C:\code\NPC\backend\api\performance\models.py changed, reloading.
INFO 2025-07-25 11:36:41,951 autoreload 13532 1008 Watching for file changes with StatReloader
INFO 2025-07-25 11:37:15,323 basehttp 13532 13536 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:37:15,539 basehttp 13532 14540 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:37:15,546 basehttp 13532 13828 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:37:15,559 basehttp 13532 11472 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 6841
INFO 2025-07-25 11:37:15,591 basehttp 13532 11212 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1817
INFO 2025-07-25 11:41:22,069 basehttp 13532 5964 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:41:22,854 basehttp 13532 7772 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 11:47:20,598 basehttp 13532 8676 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:47:20,673 basehttp 13532 14752 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 1196
INFO 2025-07-25 11:47:22,345 basehttp 13532 9900 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 11:47:22,399 basehttp 13532 3848 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 1817
INFO 2025-07-25 11:47:25,516 views 13532 9932 用户 admin 登出
INFO 2025-07-25 11:47:25,517 basehttp 13532 9932 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
WARNING 2025-07-25 11:47:30,319 log 13532 14520 Bad Request: /api/v1/users/auth/login/
WARNING 2025-07-25 11:47:30,319 basehttp 13532 14520 "POST /api/v1/users/auth/login/ HTTP/1.1" 400 101
INFO 2025-07-25 11:47:31,992 views 13532 11368 用户 rep001 登录成功，IP: 127.0.0.1
INFO 2025-07-25 11:47:31,997 basehttp 13532 11368 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 352405
INFO 2025-07-25 11:47:32,126 basehttp 13532 13844 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:47:32,185 basehttp 13532 13452 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1543
INFO 2025-07-25 11:47:32,213 basehttp 13532 3284 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 11:47:33,775 basehttp 13532 9388 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-25 11:47:33,775 basehttp 13532 7236 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 11:47:33,801 views 13532 9800 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:47:33,804 basehttp 13532 9800 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 524
INFO 2025-07-25 11:47:37,345 views 13532 14216 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 11:47:37,356 basehttp 13532 14216 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 616
INFO 2025-07-25 11:47:49,181 views 13532 11476 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 11:47:49,186 basehttp 13532 11476 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 616
INFO 2025-07-25 11:48:13,659 views 13532 14456 用户 rep001 更新履职记录: 52
INFO 2025-07-25 11:48:13,661 basehttp 13532 14456 "PUT /api/v1/performance/records/52/ HTTP/1.1" 200 616
INFO 2025-07-25 11:48:13,719 views 13532 4396 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:48:13,727 basehttp 13532 4396 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 524
INFO 2025-07-25 11:48:16,409 views 13532 7096 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 11:48:16,413 basehttp 13532 7096 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 616
INFO 2025-07-25 11:48:20,891 views 13532 10620 用户 rep001 更新履职记录: 52
INFO 2025-07-25 11:48:20,900 basehttp 13532 10620 "PUT /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 11:48:20,949 views 13532 8628 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:48:20,953 basehttp 13532 8628 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 11:49:36,791 autoreload 13532 1008 C:\code\NPC\backend\api\performance\models.py changed, reloading.
INFO 2025-07-25 11:49:37,530 autoreload 7180 12892 Watching for file changes with StatReloader
INFO 2025-07-25 11:50:16,344 autoreload 7180 12892 C:\code\NPC\backend\api\performance\models.py changed, reloading.
INFO 2025-07-25 11:50:17,027 autoreload 12060 9424 Watching for file changes with StatReloader
INFO 2025-07-25 11:50:20,851 basehttp 12060 11348 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:50:21,456 basehttp 12060 8224 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364553
INFO 2025-07-25 11:50:51,061 autoreload 12060 9424 C:\code\NPC\backend\api\performance\models.py changed, reloading.
INFO 2025-07-25 11:50:51,737 autoreload 10740 10796 Watching for file changes with StatReloader
INFO 2025-07-25 11:50:54,643 basehttp 10740 6672 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:50:55,267 basehttp 10740 11044 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364532
INFO 2025-07-25 11:51:02,117 basehttp 10740 14624 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:51:02,405 views 10740 7772 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:51:02,405 basehttp 10740 14644 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 817
INFO 2025-07-25 11:51:02,413 basehttp 10740 5964 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 11:51:02,417 basehttp 10740 10560 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:51:02,432 basehttp 10740 7772 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 11:51:02,495 basehttp 10740 6168 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 11:51:02,510 basehttp 10740 11964 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1813
INFO 2025-07-25 11:51:12,775 basehttp 10740 11904 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:51:13,588 basehttp 10740 7488 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364532
INFO 2025-07-25 11:53:15,260 autoreload 10740 10796 C:\code\NPC\backend\api\performance\models.py changed, reloading.
INFO 2025-07-25 11:53:16,053 autoreload 1688 13228 Watching for file changes with StatReloader
INFO 2025-07-25 11:53:58,458 basehttp 1688 14676 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:53:59,188 basehttp 1688 11080 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 11:54:45,696 basehttp 1688 12000 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:54:46,514 basehttp 1688 15256 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 11:55:36,374 basehttp 1688 7704 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:55:36,616 basehttp 1688 11424 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-25 11:55:36,625 views 1688 13264 用户 rep001 查询履职记录列表
INFO 2025-07-25 11:55:36,635 basehttp 1688 14232 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 11:55:36,645 basehttp 1688 11640 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:55:36,648 basehttp 1688 13264 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 11:55:36,716 basehttp 1688 6620 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 11:55:36,733 basehttp 1688 8676 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1537
INFO 2025-07-25 11:55:57,362 basehttp 1688 6652 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 11:55:58,106 basehttp 1688 10476 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:03:11,411 basehttp 1688 4900 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:03:12,333 basehttp 1688 13748 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:05:57,007 basehttp 1688 14208 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:07:29,901 basehttp 1688 8392 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:07:30,645 basehttp 1688 11784 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:08:58,330 views 1688 15316 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 12:08:58,330 basehttp 1688 15316 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:09:07,559 views 1688 14408 用户 rep001 更新履职记录: 52
INFO 2025-07-25 12:09:07,559 basehttp 1688 14408 "PUT /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:09:07,610 views 1688 164 用户 rep001 查询履职记录列表
INFO 2025-07-25 12:09:07,624 basehttp 1688 164 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 12:09:10,836 basehttp 1688 13576 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:09:11,566 basehttp 1688 1512 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:11:24,210 basehttp 1688 12976 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:11:24,861 basehttp 1688 12492 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:11:27,628 basehttp 1688 11820 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:11:27,836 basehttp 1688 15040 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-25 12:11:27,844 basehttp 1688 11720 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-25 12:11:27,848 views 1688 8936 用户 rep001 查询履职记录列表
INFO 2025-07-25 12:11:27,853 basehttp 1688 12144 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:11:27,858 basehttp 1688 8936 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 12:11:27,924 basehttp 1688 14756 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 691
INFO 2025-07-25 12:11:27,933 basehttp 1688 12896 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1537
INFO 2025-07-25 12:11:29,302 views 1688 8224 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 12:11:29,307 basehttp 1688 8224 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:11:34,384 views 1688 14004 用户 rep001 更新履职记录: 52
INFO 2025-07-25 12:11:34,402 basehttp 1688 14004 "PUT /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:11:34,450 views 1688 14088 用户 rep001 查询履职记录列表
INFO 2025-07-25 12:11:34,455 basehttp 1688 14088 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 12:11:39,655 basehttp 1688 12980 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:11:40,332 basehttp 1688 13296 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:11:47,836 views 1688 7940 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 12:11:47,836 basehttp 1688 7940 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:11:54,170 views 1688 14276 用户 rep001 更新履职记录: 52
INFO 2025-07-25 12:11:54,170 basehttp 1688 14276 "PUT /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:11:54,231 views 1688 6464 用户 rep001 查询履职记录列表
INFO 2025-07-25 12:11:54,237 basehttp 1688 6464 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 12:11:56,638 basehttp 1688 10132 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:11:57,325 basehttp 1688 13488 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 12:12:10,190 views 1688 13904 用户 rep001 查看履职记录详情: 52
INFO 2025-07-25 12:12:10,203 basehttp 1688 13904 "GET /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:12:19,399 views 1688 12692 用户 rep001 更新履职记录: 52
INFO 2025-07-25 12:12:19,399 basehttp 1688 12692 "PUT /api/v1/performance/records/52/ HTTP/1.1" 200 604
INFO 2025-07-25 12:12:19,453 views 1688 14224 用户 rep001 查询履职记录列表
INFO 2025-07-25 12:12:19,464 basehttp 1688 14224 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 512
INFO 2025-07-25 12:12:21,782 basehttp 1688 15208 "GET /api/v1/users/profile/ HTTP/1.1" 200 351914
INFO 2025-07-25 12:12:22,453 basehttp 1688 4596 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 18:34:15,501 autoreload 3696 9736 Watching for file changes with StatReloader
INFO 2025-07-25 18:34:20,035 basehttp 3696 3128 "GET /api/v1/bigscreen/ HTTP/1.1" 200 364120
INFO 2025-07-25 18:40:34,763 autoreload 9000 14544 Watching for file changes with StatReloader
INFO 2025-07-25 18:40:48,222 basehttp 9000 6588 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:40:57,687 basehttp 9000 10160 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:41:15,365 basehttp 9000 6316 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:41:55,350 basehttp 9000 13448 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:42:16,968 views 9000 7372 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 18:42:16,968 basehttp 9000 7372 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 18:42:17,154 basehttp 9000 7680 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 138
INFO 2025-07-25 18:42:18,594 basehttp 9000 12660 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 18:42:18,663 basehttp 9000 12012 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-25 18:42:20,541 basehttp 9000 8512 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 18:42:20,758 basehttp 9000 13644 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7458
INFO 2025-07-25 18:43:40,386 basehttp 9000 13988 "GET /api/v1/people-opinions/?page=1&page_size=20&search= HTTP/1.1" 200 10534
INFO 2025-07-25 18:43:41,883 basehttp 9000 11968 "GET /api/v1/workplan/?page=1&page_size=10 HTTP/1.1" 200 140
INFO 2025-07-25 18:43:41,898 basehttp 9000 11420 "GET /api/v1/workplan/statistics/ HTTP/1.1" 200 718
INFO 2025-07-25 18:43:42,693 views 9000 10952 用户 admin 查看意见建议列表，返回 0 条记录
INFO 2025-07-25 18:43:42,699 basehttp 9000 10952 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 124
INFO 2025-07-25 18:43:42,703 basehttp 9000 13552 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-25 18:43:44,521 basehttp 9000 15212 "GET /api/v1/people-opinions/?page=1&page_size=20&search= HTTP/1.1" 200 10534
INFO 2025-07-25 18:43:47,053 basehttp 9000 3092 "GET /api/v1/workplan/?page=1&page_size=10 HTTP/1.1" 200 140
INFO 2025-07-25 18:43:47,067 basehttp 9000 14140 "GET /api/v1/workplan/statistics/ HTTP/1.1" 200 718
INFO 2025-07-25 18:43:50,712 views 9000 8700 工作人员 管理员 查看 2025年 代表工作总结列表，共 39 位代表
INFO 2025-07-25 18:43:50,882 basehttp 9000 8700 "GET /api/v1/ai-summaries/representatives/?year=2025 HTTP/1.1" 200 23742165
INFO 2025-07-25 18:44:04,519 views 9000 10064 获取AI总结详情成功：38
INFO 2025-07-25 18:44:04,536 basehttp 9000 10064 "GET /api/v1/ai-summaries/2025/?representative_id=70 HTTP/1.1" 200 1383055
INFO 2025-07-25 18:44:29,298 views 9000 10908 用户 admin 查看意见建议列表，返回 0 条记录
INFO 2025-07-25 18:44:29,298 basehttp 9000 10908 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 124
INFO 2025-07-25 18:44:29,324 basehttp 9000 6764 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-25 18:44:37,184 views 9000 9348 用户 admin 登出
INFO 2025-07-25 18:44:37,184 basehttp 9000 9348 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 18:44:39,401 basehttp 9000 13768 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:45:34,087 basehttp 9000 9788 "GET /api/v1/bigscreen/district-members/?district=%E9%82%A3%E6%B4%AA%E7%89%87%E5%8C%BA HTTP/1.1" 200 13979200
INFO 2025-07-25 18:45:47,198 views 9000 12496 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 18:45:47,199 basehttp 9000 12496 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 18:45:47,275 basehttp 9000 10216 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 138
INFO 2025-07-25 18:45:48,455 basehttp 9000 15252 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 18:45:48,488 basehttp 9000 15036 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-25 18:45:49,964 basehttp 9000 2536 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 18:45:50,141 basehttp 9000 8952 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7458
INFO 2025-07-25 18:45:55,369 basehttp 9000 9388 "GET /api/v1/users/manage/?page=1&page_size=20&role=staff HTTP/1.1" 200 880
INFO 2025-07-25 18:46:13,021 basehttp 9000 12508 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7458
INFO 2025-07-25 18:46:21,905 views 9000 8600 用户 admin 登出
INFO 2025-07-25 18:46:21,905 basehttp 9000 8600 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-25 18:46:39,614 basehttp 9000 12000 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:48:54,876 basehttp 9000 3664 "GET /api/v1/bigscreen/ HTTP/1.1" 200 23743046
INFO 2025-07-25 18:57:31,929 autoreload 9000 14544 C:\code\NPC\backend\api\bigscreen\views.py changed, reloading.
INFO 2025-07-25 18:57:32,724 autoreload 9844 13208 Watching for file changes with StatReloader
INFO 2025-07-25 18:57:52,922 autoreload 9844 13208 C:\code\NPC\backend\api\bigscreen\views.py changed, reloading.
INFO 2025-07-25 18:57:53,466 autoreload 1360 5060 Watching for file changes with StatReloader
INFO 2025-07-25 18:58:07,386 autoreload 1360 5060 C:\code\NPC\backend\api\bigscreen\urls.py changed, reloading.
INFO 2025-07-25 18:58:07,934 autoreload 7612 15276 Watching for file changes with StatReloader
INFO 2025-07-25 18:58:31,104 autoreload 7612 15276 C:\code\NPC\backend\api\bigscreen\services.py changed, reloading.
INFO 2025-07-25 18:58:31,637 autoreload 1424 11648 Watching for file changes with StatReloader
INFO 2025-07-25 18:58:45,585 autoreload 1424 11648 C:\code\NPC\backend\api\bigscreen\services.py changed, reloading.
INFO 2025-07-25 18:58:46,169 autoreload 10664 1808 Watching for file changes with StatReloader
INFO 2025-07-25 18:59:49,553 basehttp 10664 10960 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 18:59:59,032 basehttp 10664 4916 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:00:11,821 basehttp 10664 15236 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:00:32,583 basehttp 10664 5064 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:00:48,251 basehttp 10664 8052 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:01:01,866 basehttp 10664 13840 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:01:01,973 basehttp 10664 1872 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-25 19:01:03,764 basehttp 10664 3472 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-25 19:01:04,781 basehttp 10664 7012 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-25 19:01:05,796 basehttp 10664 14012 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-25 19:01:07,923 basehttp 10664 13528 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-25 19:01:09,052 basehttp 10664 12748 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-25 19:01:10,052 basehttp 10664 12356 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-25 19:01:12,193 basehttp 10664 3560 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-25 19:01:13,310 basehttp 10664 832 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-25 19:01:14,428 basehttp 10664 1716 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-25 19:01:15,449 basehttp 10664 7552 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-25 19:01:16,464 basehttp 10664 6352 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-25 19:01:17,574 basehttp 10664 2748 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-25 19:01:18,693 basehttp 10664 11996 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-25 19:01:19,791 basehttp 10664 10080 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-25 19:01:20,917 basehttp 10664 9608 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-25 19:01:22,032 basehttp 10664 424 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-25 19:01:23,032 basehttp 10664 11532 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-25 19:01:24,143 basehttp 10664 2276 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-25 19:01:25,148 basehttp 10664 11076 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-25 19:01:26,282 basehttp 10664 9748 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-25 19:01:27,399 basehttp 10664 10852 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-25 19:01:28,526 basehttp 10664 7744 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-25 19:01:29,536 basehttp 10664 13136 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-25 19:01:30,652 basehttp 10664 8428 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-25 19:01:31,782 basehttp 10664 3484 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-25 19:01:32,903 basehttp 10664 14516 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-25 19:01:34,012 basehttp 10664 13456 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-25 19:01:35,137 basehttp 10664 4112 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-25 19:01:36,147 basehttp 10664 10884 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-25 19:01:37,152 basehttp 10664 13968 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-25 19:01:38,151 basehttp 10664 7052 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-25 19:01:39,163 basehttp 10664 1800 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-25 19:01:40,263 basehttp 10664 5040 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-25 19:01:41,390 basehttp 10664 12596 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-25 19:01:42,434 basehttp 10664 1724 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-25 19:01:43,428 basehttp 10664 3060 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-25 19:01:44,561 basehttp 10664 8004 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-25 19:03:21,955 autoreload 6936 15036 Watching for file changes with StatReloader
INFO 2025-07-25 19:03:48,158 basehttp 6936 2160 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:03:49,313 basehttp 6936 13660 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:04:08,114 basehttp 6936 9948 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:06:44,987 views 6936 2856 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-25 19:06:44,987 basehttp 6936 2856 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-25 19:06:45,162 basehttp 6936 3560 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 138
INFO 2025-07-25 19:06:46,485 basehttp 6936 14180 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 19:06:46,525 basehttp 6936 5516 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-25 19:06:48,665 basehttp 6936 14424 "GET /api/v1/people-opinions/?page=1&page_size=20&search= HTTP/1.1" 200 10534
INFO 2025-07-25 19:06:50,849 basehttp 6936 9068 "GET /api/v1/people-opinions/?page=1&page_size=20&search= HTTP/1.1" 200 10534
INFO 2025-07-25 19:06:53,473 basehttp 6936 14200 "GET /api/v1/workplan/?page=1&page_size=10 HTTP/1.1" 200 140
INFO 2025-07-25 19:06:53,479 basehttp 6936 2964 "GET /api/v1/workplan/statistics/ HTTP/1.1" 200 718
INFO 2025-07-25 19:06:55,406 views 6936 3448 工作人员 管理员 查看 2025年 代表工作总结列表，共 39 位代表
INFO 2025-07-25 19:06:55,571 basehttp 6936 3448 "GET /api/v1/ai-summaries/representatives/?year=2025 HTTP/1.1" 200 23742165
INFO 2025-07-25 19:06:57,475 views 6936 10908 获取AI总结详情成功：35
INFO 2025-07-25 19:06:57,475 basehttp 6936 10908 "GET /api/v1/ai-summaries/2025/?representative_id=35 HTTP/1.1" 200 1797
INFO 2025-07-25 19:09:37,114 basehttp 6936 1900 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:10:31,492 basehttp 6936 3820 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-25 19:10:44,464 basehttp 6936 4564 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 19:10:45,217 basehttp 6936 2400 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:10:45,364 basehttp 6936 3148 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-25 19:10:45,498 basehttp 6936 8276 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-25 19:10:45,626 basehttp 6936 15336 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-25 19:10:45,756 basehttp 6936 828 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-25 19:10:45,920 basehttp 6936 10812 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-25 19:10:46,046 basehttp 6936 13872 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-25 19:10:46,176 basehttp 6936 2196 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-25 19:10:46,310 basehttp 6936 10232 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-25 19:10:46,434 basehttp 6936 5300 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-25 19:10:46,555 basehttp 6936 5460 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-25 19:10:46,681 basehttp 6936 11364 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-25 19:10:46,817 basehttp 6936 5704 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-25 19:10:46,951 basehttp 6936 8976 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-25 19:10:47,079 basehttp 6936 12584 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-25 19:10:47,206 basehttp 6936 13488 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-25 19:10:47,334 basehttp 6936 2696 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-25 19:10:47,464 basehttp 6936 9916 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-25 19:10:47,591 basehttp 6936 5232 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-25 19:10:47,711 basehttp 6936 15356 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-25 19:10:47,843 basehttp 6936 2828 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-25 19:10:47,965 basehttp 6936 12320 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-25 19:10:48,095 basehttp 6936 8608 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-25 19:10:48,218 basehttp 6936 14128 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-25 19:10:48,350 basehttp 6936 11020 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-25 19:10:48,484 basehttp 6936 8880 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-25 19:10:48,623 basehttp 6936 8268 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-25 19:10:48,742 basehttp 6936 6496 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-25 19:10:48,868 basehttp 6936 992 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-25 19:10:48,995 basehttp 6936 11912 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-25 19:10:49,135 basehttp 6936 13644 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-25 19:10:49,250 basehttp 6936 12744 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-25 19:10:49,383 basehttp 6936 6704 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-25 19:10:49,504 basehttp 6936 12356 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-25 19:10:49,643 basehttp 6936 6464 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-25 19:10:49,779 basehttp 6936 9620 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-25 19:10:49,911 basehttp 6936 1228 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-25 19:10:50,050 basehttp 6936 14300 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-25 19:10:50,168 basehttp 6936 5268 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-25 19:14:13,723 basehttp 6936 10424 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 19:14:14,605 basehttp 6936 13532 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-25 19:15:38,659 basehttp 6936 10796 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-25 19:15:39,422 basehttp 6936 13320 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11221
INFO 2025-07-27 21:02:16,786 autoreload 14592 7288 Watching for file changes with StatReloader
WARNING 2025-07-27 21:06:05,087 log 14592 7584 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-27 21:06:05,087 log 14592 6172 Unauthorized: /api/v1/workplan/reminders/
WARNING 2025-07-27 21:06:05,090 basehttp 14592 7584 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
WARNING 2025-07-27 21:06:05,090 basehttp 14592 6172 "GET /api/v1/workplan/reminders/ HTTP/1.1" 401 171
INFO 2025-07-27 21:06:05,190 basehttp 14592 10028 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 486
INFO 2025-07-27 21:06:05,220 basehttp 14592 11368 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 138
INFO 2025-07-27 21:06:05,238 basehttp 14592 4748 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-27 21:06:06,460 basehttp 14592 5500 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-27 21:06:06,502 basehttp 14592 1620 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-27 21:06:08,150 basehttp 14592 9316 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-27 21:06:08,319 basehttp 14592 7384 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7458
INFO 2025-07-27 21:07:03,398 signals 14592 7596 新用户创建 - ID: 110, 用户名: test, 角色: 人大代表
INFO 2025-07-27 21:07:03,398 signals 14592 7596 人大代表信息创建 - 姓名: 测试, 层级: 乡镇人大代表, 关联用户: test
INFO 2025-07-27 21:07:03,414 views 14592 7596 工作人员 admin 创建用户 test
INFO 2025-07-27 21:07:03,423 basehttp 14592 7596 "POST /api/v1/users/manage/ HTTP/1.1" 201 1080
INFO 2025-07-27 21:07:03,635 basehttp 14592 3516 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7423
INFO 2025-07-27 21:07:06,280 views 14592 15012 用户 admin 登出
INFO 2025-07-27 21:07:06,280 basehttp 14592 15012 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-27 21:07:11,847 views 14592 14492 用户 test 登录成功，IP: 127.0.0.1
INFO 2025-07-27 21:07:11,847 basehttp 14592 14492 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1637
INFO 2025-07-27 21:07:11,987 basehttp 14592 14852 "GET /api/v1/users/profile/ HTTP/1.1" 200 1140
INFO 2025-07-27 21:07:12,023 basehttp 14592 14380 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-27 21:07:12,066 basehttp 14592 1468 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1382
INFO 2025-07-27 21:07:14,537 views 14592 9060 用户 test 查看意见建议列表，返回 0 条记录
INFO 2025-07-27 21:07:14,537 basehttp 14592 9060 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 124
INFO 2025-07-27 21:07:50,325 basehttp 14592 11632 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-27 21:07:50,325 basehttp 14592 4272 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-27 21:07:50,341 views 14592 9800 用户 test 查询履职记录列表
INFO 2025-07-27 21:07:50,352 basehttp 14592 9800 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-27 21:07:50,784 basehttp 14592 8508 "GET /api/v1/users/profile/ HTTP/1.1" 200 1140
WARNING 2025-07-27 21:07:51,204 views 14592 15336 AI总结不存在，代表：测试，年份：2025
WARNING 2025-07-27 21:07:51,204 log 14592 15336 Not Found: /api/v1/ai-summaries/2025/
WARNING 2025-07-27 21:07:51,204 basehttp 14592 15336 "GET /api/v1/ai-summaries/2025/ HTTP/1.1" 404 88
WARNING 2025-07-27 21:07:51,225 views 14592 13636 AI总结不存在，代表：测试，年份：2024
WARNING 2025-07-27 21:07:51,225 log 14592 13636 Not Found: /api/v1/ai-summaries/2024/
WARNING 2025-07-27 21:07:51,227 basehttp 14592 13636 "GET /api/v1/ai-summaries/2024/ HTTP/1.1" 404 88
WARNING 2025-07-27 21:07:51,246 views 14592 8656 AI总结不存在，代表：测试，年份：2023
WARNING 2025-07-27 21:07:51,246 log 14592 8656 Not Found: /api/v1/ai-summaries/2023/
WARNING 2025-07-27 21:07:51,248 basehttp 14592 8656 "GET /api/v1/ai-summaries/2023/ HTTP/1.1" 404 88
WARNING 2025-07-27 21:07:51,265 views 14592 1812 AI总结不存在，代表：测试，年份：2022
WARNING 2025-07-27 21:07:51,266 log 14592 1812 Not Found: /api/v1/ai-summaries/2022/
WARNING 2025-07-27 21:07:51,266 basehttp 14592 1812 "GET /api/v1/ai-summaries/2022/ HTTP/1.1" 404 88
WARNING 2025-07-27 21:07:51,279 views 14592 7740 AI总结不存在，代表：测试，年份：2021
WARNING 2025-07-27 21:07:51,279 log 14592 7740 Not Found: /api/v1/ai-summaries/2021/
WARNING 2025-07-27 21:07:51,284 basehttp 14592 7740 "GET /api/v1/ai-summaries/2021/ HTTP/1.1" 404 88
WARNING 2025-07-27 21:07:51,340 views 14592 4900 AI总结不存在，代表：测试，年份：2020
WARNING 2025-07-27 21:07:51,340 log 14592 4900 Not Found: /api/v1/ai-summaries/2020/
WARNING 2025-07-27 21:07:51,342 basehttp 14592 4900 "GET /api/v1/ai-summaries/2020/ HTTP/1.1" 404 88
INFO 2025-07-27 21:07:51,459 basehttp 14592 8380 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-27 21:07:51,474 basehttp 14592 9652 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-27 21:07:51,485 views 14592 11932 用户 test 查询履职记录列表
INFO 2025-07-27 21:07:51,490 basehttp 14592 11932 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-27 21:25:24,036 autoreload 8288 9852 Watching for file changes with StatReloader
WARNING 2025-07-27 21:33:21,267 log 8288 8036 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-27 21:33:21,268 basehttp 8288 8036 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
ERROR 2025-07-27 21:33:21,846 log 8288 7100 Internal Server Error: /api/v1/users/auth/refresh/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework_simplejwt\views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework_simplejwt\serializers.py", line 116, in validate
    user := get_user_model().objects.get(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 633, in get
    raise self.model.DoesNotExist(
api.users.models.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-27 21:33:21,850 basehttp 8288 7100 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 500 129387
INFO 2025-07-27 21:33:22,115 basehttp 8288 9396 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11464
INFO 2025-07-27 21:33:30,726 views 8288 5448 用户 test 登录成功，IP: 127.0.0.1
INFO 2025-07-27 21:33:30,726 basehttp 8288 5448 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1637
INFO 2025-07-27 21:33:30,871 basehttp 8288 9428 "GET /api/v1/users/profile/ HTTP/1.1" 200 1140
INFO 2025-07-27 21:33:30,925 basehttp 8288 9844 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-27 21:33:30,931 basehttp 8288 13772 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1382
INFO 2025-07-27 21:33:33,699 basehttp 8288 516 "GET /api/v1/users/profile/ HTTP/1.1" 200 1140
INFO 2025-07-27 21:33:35,257 basehttp 8288 6988 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-27 21:33:35,257 views 8288 5940 用户 test 查询履职记录列表
INFO 2025-07-27 21:33:35,257 basehttp 8288 12536 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-27 21:33:35,257 basehttp 8288 5940 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-27 21:42:42,846 autoreload 11688 12864 Watching for file changes with StatReloader
WARNING 2025-07-27 21:42:56,841 log 11688 8168 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-27 21:42:56,842 basehttp 11688 8168 "GET /api/v1/users/profile/ HTTP/1.1" 401 55
WARNING 2025-07-27 21:42:56,996 log 11688 9644 Unauthorized: /api/v1/performance/choices/types/
WARNING 2025-07-27 21:42:56,998 basehttp 11688 9644 "GET /api/v1/performance/choices/types/ HTTP/1.1" 401 55
WARNING 2025-07-27 21:42:56,998 log 11688 1068 Unauthorized: /api/v1/performance/choices/status/
WARNING 2025-07-27 21:42:57,000 log 11688 8308 Unauthorized: /api/v1/performance/records/
WARNING 2025-07-27 21:42:57,002 log 11688 14504 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-27 21:42:57,002 basehttp 11688 1068 "GET /api/v1/performance/choices/status/ HTTP/1.1" 401 55
WARNING 2025-07-27 21:42:57,003 basehttp 11688 8308 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 401 55
WARNING 2025-07-27 21:42:57,003 basehttp 11688 14504 "GET /api/v1/users/profile/ HTTP/1.1" 401 55
ERROR 2025-07-27 21:42:57,062 log 11688 1676 Internal Server Error: /api/v1/users/auth/refresh/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework_simplejwt\views.py", line 44, in post
    serializer.is_valid(raise_exception=True)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 225, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 447, in run_validation
    value = self.validate(value)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework_simplejwt\serializers.py", line 116, in validate
    user := get_user_model().objects.get(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 633, in get
    raise self.model.DoesNotExist(
api.users.models.User.DoesNotExist: User matching query does not exist.
ERROR 2025-07-27 21:42:57,072 basehttp 11688 1676 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 500 129438
WARNING 2025-07-27 21:42:57,218 log 11688 8584 Unauthorized: /api/v1/opinions/statistics/
WARNING 2025-07-27 21:42:57,222 log 11688 12212 Unauthorized: /api/v1/performance/records/stats/
WARNING 2025-07-27 21:42:57,224 basehttp 11688 12212 "GET /api/v1/performance/records/stats/ HTTP/1.1" 401 43
WARNING 2025-07-27 21:42:57,224 basehttp 11688 8584 "GET /api/v1/opinions/statistics/ HTTP/1.1" 401 43
WARNING 2025-07-27 21:43:03,047 log 11688 2432 Bad Request: /api/v1/users/auth/login/
WARNING 2025-07-27 21:43:03,066 basehttp 11688 2432 "POST /api/v1/users/auth/login/ HTTP/1.1" 400 101
INFO 2025-07-27 21:43:09,076 views 11688 5988 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-27 21:43:09,076 basehttp 11688 5988 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-27 21:43:09,213 basehttp 11688 8976 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 138
INFO 2025-07-27 21:43:10,319 basehttp 11688 14872 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-27 21:43:10,370 basehttp 11688 864 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-27 21:43:12,827 basehttp 11688 5160 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-27 21:43:12,996 basehttp 11688 6260 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7458
INFO 2025-07-27 21:43:47,934 signals 11688 3988 新用户创建 - ID: 110, 用户名: test, 角色: 人大代表
INFO 2025-07-27 21:43:47,949 signals 11688 3988 人大代表信息创建 - 姓名: test, 层级: 乡镇人大代表, 关联用户: test
INFO 2025-07-27 21:43:47,949 views 11688 3988 工作人员 admin 创建用户 test
INFO 2025-07-27 21:43:47,956 basehttp 11688 3988 "POST /api/v1/users/manage/ HTTP/1.1" 201 1077
INFO 2025-07-27 21:43:48,157 basehttp 11688 8992 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7421
INFO 2025-07-27 21:43:51,162 views 11688 2964 用户 admin 登出
INFO 2025-07-27 21:43:51,162 basehttp 11688 2964 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-27 21:43:56,363 views 11688 12856 用户 test 登录成功，IP: 127.0.0.1
INFO 2025-07-27 21:43:56,363 basehttp 11688 12856 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1634
INFO 2025-07-27 21:43:56,440 basehttp 11688 11132 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-27 21:43:56,489 basehttp 11688 8768 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-27 21:43:56,503 basehttp 11688 14676 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1382
INFO 2025-07-27 22:45:57,517 autoreload 5864 7228 Watching for file changes with StatReloader
WARNING 2025-07-27 22:52:19,324 log 5864 4068 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-27 22:52:19,325 basehttp 5864 4068 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
WARNING 2025-07-27 22:52:19,333 log 5864 4156 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-27 22:52:19,334 basehttp 5864 4156 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-27 22:52:19,392 basehttp 5864 3628 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 489
INFO 2025-07-27 22:52:19,438 basehttp 5864 6744 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-27 22:52:19,438 basehttp 5864 8236 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-27 22:52:19,489 basehttp 5864 8048 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-27 22:52:19,520 basehttp 5864 8540 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1382
INFO 2025-07-27 22:52:22,014 basehttp 5864 5380 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-27 22:52:22,016 basehttp 5864 3296 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-27 22:52:22,016 views 5864 2900 用户 test 查询履职记录列表
INFO 2025-07-27 22:52:22,021 basehttp 5864 2900 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-27 22:53:03,069 basehttp 5864 7608 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-27 22:53:04,012 basehttp 5864 4596 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11462
INFO 2025-07-27 22:53:04,150 basehttp 5864 1588 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-27 22:53:04,279 basehttp 5864 3812 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-27 22:53:04,418 basehttp 5864 3904 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-27 22:53:04,570 basehttp 5864 2200 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-27 22:53:04,721 basehttp 5864 8088 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-27 22:53:04,868 basehttp 5864 8564 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-27 22:53:04,996 basehttp 5864 1228 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-27 22:53:05,120 basehttp 5864 5496 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-27 22:53:05,279 basehttp 5864 5920 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-27 22:53:05,407 basehttp 5864 6396 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-27 22:53:05,535 basehttp 5864 4584 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-27 22:53:05,673 basehttp 5864 3868 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-27 22:53:05,821 basehttp 5864 3748 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-27 22:53:05,954 basehttp 5864 4620 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-27 22:53:06,106 basehttp 5864 1560 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-27 22:53:06,236 basehttp 5864 8252 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-27 22:53:06,359 basehttp 5864 1648 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-27 22:53:06,486 basehttp 5864 5936 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-27 22:53:06,628 basehttp 5864 4988 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-27 22:53:06,764 basehttp 5864 5012 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-27 22:53:06,894 basehttp 5864 4716 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-27 22:53:07,039 basehttp 5864 3560 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-27 22:53:07,164 basehttp 5864 688 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-27 22:53:07,287 basehttp 5864 8484 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-27 22:53:07,408 basehttp 5864 6324 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-27 22:53:07,531 basehttp 5864 5564 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-27 22:53:07,678 basehttp 5864 1444 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-27 22:53:07,818 basehttp 5864 5412 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-27 22:53:07,955 basehttp 5864 6504 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-27 22:53:08,105 basehttp 5864 7656 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-27 22:53:08,242 basehttp 5864 7648 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-27 22:53:08,401 basehttp 5864 3128 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-27 22:53:08,533 basehttp 5864 3300 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-27 22:53:08,660 basehttp 5864 1076 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-27 22:53:08,795 basehttp 5864 832 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-27 22:53:08,924 basehttp 5864 6916 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-27 22:53:09,059 basehttp 5864 7676 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-27 22:53:09,194 basehttp 5864 1508 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-27 23:06:57,243 views 5864 12964 用户 test 查看意见建议列表，返回 0 条记录
INFO 2025-07-27 23:06:57,247 basehttp 5864 12964 "GET /api/v1/opinions/suggestions/?page=1&page_size=10&ordering=-created_at HTTP/1.1" 200 124
INFO 2025-07-27 23:10:20,293 basehttp 5864 12336 "GET /api/v1/bigscreen/district-members/?district=%E6%B2%9B%E9%B8%BF%E7%89%87%E5%8C%BA HTTP/1.1" 200 2272
INFO 2025-07-27 23:10:25,740 basehttp 5864 6012 "GET /api/v1/bigscreen/district-members/?district=%E9%82%A3%E6%B4%AA%E7%89%87%E5%8C%BA HTTP/1.1" 200 4568
INFO 2025-07-28 11:27:45,539 basehttp 5864 6240 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11462
INFO 2025-07-28 11:27:45,648 basehttp 5864 12856 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-28 11:27:45,781 basehttp 5864 11524 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-28 11:27:45,914 basehttp 5864 11988 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-28 11:27:46,044 basehttp 5864 10744 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-28 11:27:46,167 basehttp 5864 10796 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-28 11:27:46,313 basehttp 5864 13252 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-28 11:27:46,434 basehttp 5864 11244 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-28 11:27:46,560 basehttp 5864 5448 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-28 11:27:46,683 basehttp 5864 6276 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-28 11:27:46,808 basehttp 5864 7756 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-28 11:27:46,935 basehttp 5864 11144 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-28 11:27:47,069 basehttp 5864 10504 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-28 11:27:47,194 basehttp 5864 7036 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-28 11:27:47,315 basehttp 5864 4144 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-28 11:27:47,446 basehttp 5864 3900 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-28 11:27:47,569 basehttp 5864 10560 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-28 11:27:47,707 basehttp 5864 12992 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-28 11:27:47,826 basehttp 5864 6384 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-28 11:27:47,955 basehttp 5864 12260 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-28 11:27:48,078 basehttp 5864 13140 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-28 11:27:48,202 basehttp 5864 788 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-28 11:27:48,326 basehttp 5864 12256 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-28 11:27:48,458 basehttp 5864 6112 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-28 11:27:48,598 basehttp 5864 11056 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-28 11:27:48,751 basehttp 5864 11216 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-28 11:27:48,885 basehttp 5864 2028 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-28 11:27:49,013 basehttp 5864 9384 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-28 11:27:49,138 basehttp 5864 12832 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-28 11:27:49,273 basehttp 5864 8660 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-28 11:27:49,403 basehttp 5864 11484 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-28 11:27:49,537 basehttp 5864 1440 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-28 11:27:49,663 basehttp 5864 10936 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-28 11:27:49,800 basehttp 5864 11584 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-28 11:27:49,942 basehttp 5864 7020 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-28 11:27:50,087 basehttp 5864 7152 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-28 11:27:50,210 basehttp 5864 11300 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-28 11:27:50,337 basehttp 5864 12912 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-28 11:27:50,465 basehttp 5864 1500 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-28 11:33:54,148 autoreload 5864 7228 C:\code\NPC\backend\api\bigscreen\services.py changed, reloading.
INFO 2025-07-28 11:33:54,913 autoreload 13260 11764 Watching for file changes with StatReloader
INFO 2025-07-28 12:07:08,034 basehttp 13260 5956 "GET /api/v1/bigscreen/ HTTP/1.1" 200 11726
WARNING 2025-07-28 12:25:00,540 log 13260 10936 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-28 12:25:00,540 log 13260 10676 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-28 12:25:00,542 basehttp 13260 10936 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
WARNING 2025-07-28 12:25:00,542 basehttp 13260 10676 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-28 12:25:00,600 basehttp 13260 13296 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 489
INFO 2025-07-28 12:25:00,629 basehttp 13260 13048 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 12:25:00,630 basehttp 13260 12948 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 12:25:00,666 basehttp 13260 684 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-28 12:25:00,691 basehttp 13260 10264 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1382
INFO 2025-07-28 12:25:02,413 basehttp 13260 11760 "GET /api/v1/performance/choices/status/ HTTP/1.1" 200 139
INFO 2025-07-28 12:25:02,417 basehttp 13260 10344 "GET /api/v1/performance/choices/types/ HTTP/1.1" 200 553
INFO 2025-07-28 12:25:02,417 views 13260 10680 用户 test 查询履职记录列表
INFO 2025-07-28 12:25:02,425 basehttp 13260 10680 "GET /api/v1/performance/records/?page=1&page_size=10&ordering=-updated_at HTTP/1.1" 200 102
INFO 2025-07-28 12:30:06,090 autoreload 13260 11764 C:\code\NPC\backend\api\bigscreen\services.py changed, reloading.
INFO 2025-07-28 12:30:07,757 autoreload 12352 3748 Watching for file changes with StatReloader
INFO 2025-07-28 12:30:43,632 autoreload 12352 3748 C:\code\NPC\backend\api\bigscreen\services.py changed, reloading.
INFO 2025-07-28 12:30:44,372 autoreload 11136 9896 Watching for file changes with StatReloader
INFO 2025-07-28 14:03:36,679 basehttp 11136 12544 "GET /api/v1/bigscreen/ HTTP/1.1" 200 16287
INFO 2025-07-28 14:03:36,785 basehttp 11136 10820 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-28 14:03:36,915 basehttp 11136 264 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-28 14:03:37,060 basehttp 11136 12208 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-28 14:03:37,200 basehttp 11136 12492 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-28 14:03:37,345 basehttp 11136 13032 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-28 14:03:37,475 basehttp 11136 10312 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-28 14:03:37,603 basehttp 11136 12992 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-28 14:03:37,722 basehttp 11136 4976 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-28 14:03:37,854 basehttp 11136 10676 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-28 14:03:37,981 basehttp 11136 7040 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-28 14:03:38,112 basehttp 11136 3040 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-28 14:03:38,231 basehttp 11136 7400 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-28 14:03:38,358 basehttp 11136 12716 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-28 14:03:38,485 basehttp 11136 12360 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-28 14:03:38,618 basehttp 11136 3004 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-28 14:03:38,742 basehttp 11136 552 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-28 14:03:38,872 basehttp 11136 13304 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-28 14:03:38,994 basehttp 11136 5856 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-28 14:03:39,124 basehttp 11136 12376 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-28 14:03:39,244 basehttp 11136 10524 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-28 14:03:39,383 basehttp 11136 3324 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-28 14:03:39,515 basehttp 11136 4476 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-28 14:03:39,637 basehttp 11136 13116 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-28 14:03:39,769 basehttp 11136 4396 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-28 14:03:39,889 basehttp 11136 5816 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-28 14:03:40,016 basehttp 11136 12824 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-28 14:03:40,148 basehttp 11136 10468 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-28 14:03:40,313 basehttp 11136 7216 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-28 14:03:40,448 basehttp 11136 7152 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-28 14:03:40,571 basehttp 11136 7144 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-28 14:03:40,698 basehttp 11136 10296 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-28 14:03:40,822 basehttp 11136 4528 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-28 14:03:40,962 basehttp 11136 12028 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-28 14:03:41,091 basehttp 11136 13096 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-28 14:03:41,223 basehttp 11136 5508 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-28 14:03:41,348 basehttp 11136 12096 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-28 14:03:41,471 basehttp 11136 11864 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-28 14:03:41,610 basehttp 11136 3572 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
WARNING 2025-07-28 16:04:03,013 log 11136 5064 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-28 16:04:03,013 basehttp 11136 5064 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-28 16:04:03,198 basehttp 11136 7612 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 489
INFO 2025-07-28 16:04:03,309 basehttp 11136 5488 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 16:04:03,989 basehttp 11136 12028 "GET /api/v1/bigscreen/ HTTP/1.1" 200 16287
INFO 2025-07-28 16:04:04,125 basehttp 11136 12244 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-28 16:04:04,290 basehttp 11136 10836 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-28 16:04:04,428 basehttp 11136 6480 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-28 16:04:04,590 basehttp 11136 232 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-28 16:04:04,755 basehttp 11136 12904 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-28 16:04:04,933 basehttp 11136 3452 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-28 16:04:05,068 basehttp 11136 8816 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-28 16:04:05,072 basehttp 11136 11064 "GET /api/v1/bigscreen/ HTTP/1.1" 200 16287
INFO 2025-07-28 16:04:05,202 basehttp 11136 10464 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-28 16:04:05,239 basehttp 11136 12140 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-28 16:04:05,371 basehttp 11136 4156 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-28 16:04:05,547 basehttp 11136 10300 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-28 16:04:05,695 basehttp 11136 13036 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-28 16:04:05,831 basehttp 11136 10976 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-28 16:04:05,959 basehttp 11136 12844 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-28 16:04:06,079 basehttp 11136 12644 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-28 16:04:06,221 basehttp 11136 12936 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-28 16:04:06,392 basehttp 11136 10744 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-28 16:04:06,573 basehttp 11136 7036 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-28 16:04:06,720 basehttp 11136 5564 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-28 16:04:06,862 basehttp 11136 8184 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-28 16:04:06,903 basehttp 11136 1500 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-28 16:04:06,993 basehttp 11136 11352 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-28 16:04:07,152 basehttp 11136 13172 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-28 16:04:07,291 basehttp 11136 3904 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-28 16:04:07,421 basehttp 11136 10580 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-28 16:04:07,564 basehttp 11136 10444 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-28 16:04:07,720 basehttp 11136 13296 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-28 16:04:07,846 basehttp 11136 6908 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-28 16:04:07,912 basehttp 11136 8400 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-28 16:04:07,977 basehttp 11136 10592 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-28 16:04:08,137 basehttp 11136 11132 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-28 16:04:08,281 basehttp 11136 6116 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-28 16:04:08,441 basehttp 11136 11812 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-28 16:04:08,575 basehttp 11136 12788 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-28 16:04:08,721 basehttp 11136 11660 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-28 16:04:08,863 basehttp 11136 12692 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-28 16:04:09,021 basehttp 11136 11624 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-28 16:04:09,147 basehttp 11136 6100 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-28 16:04:09,277 basehttp 11136 1072 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-28 16:04:09,420 basehttp 11136 11476 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-28 16:04:09,551 basehttp 11136 4284 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-28 16:04:09,930 basehttp 11136 8504 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-28 16:04:10,970 basehttp 11136 2960 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-28 16:04:11,988 basehttp 11136 6820 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-28 16:04:12,997 basehttp 11136 11876 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-28 16:04:15,164 basehttp 11136 13088 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-28 16:04:16,223 basehttp 11136 11012 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-28 16:04:17,231 basehttp 11136 12176 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-28 16:04:19,294 basehttp 11136 5772 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-28 16:04:20,299 basehttp 11136 11732 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-28 16:04:21,335 basehttp 11136 12412 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-28 16:04:22,356 basehttp 11136 10440 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-28 16:04:23,342 basehttp 11136 2908 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-28 16:04:24,434 basehttp 11136 3428 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-28 16:04:25,482 basehttp 11136 3260 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-28 16:04:26,529 basehttp 11136 10320 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-28 16:04:27,630 basehttp 11136 11704 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-28 16:04:28,675 basehttp 11136 2260 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-28 16:04:29,709 basehttp 11136 10844 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-28 16:04:30,742 basehttp 11136 10308 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-28 16:04:31,805 basehttp 11136 364 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-28 16:04:32,855 basehttp 11136 7912 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-28 16:04:33,884 basehttp 11136 3736 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-28 16:04:34,925 basehttp 11136 11368 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-28 16:04:35,954 basehttp 11136 12076 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-28 16:04:36,982 basehttp 11136 5168 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-28 16:04:38,044 basehttp 11136 11176 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-28 16:04:39,085 basehttp 11136 11652 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-28 16:04:40,149 basehttp 11136 10748 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-28 16:04:41,204 basehttp 11136 6780 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-28 16:04:42,259 basehttp 11136 10588 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-28 16:04:43,363 basehttp 11136 10968 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-28 16:04:44,397 basehttp 11136 13176 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-28 16:04:45,411 basehttp 11136 2608 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-28 16:04:46,433 basehttp 11136 9648 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-28 16:04:47,471 basehttp 11136 4528 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-28 18:38:02,019 autoreload 12244 6092 Watching for file changes with StatReloader
WARNING 2025-07-28 18:38:05,828 log 12244 2936 Unauthorized: /api/v1/users/profile/
WARNING 2025-07-28 18:38:05,829 basehttp 12244 2936 "GET /api/v1/users/profile/ HTTP/1.1" 401 171
INFO 2025-07-28 18:38:05,880 basehttp 12244 10720 "POST /api/v1/users/auth/refresh/ HTTP/1.1" 200 489
INFO 2025-07-28 18:38:05,915 basehttp 12244 10120 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 18:38:06,297 basehttp 12244 11560 "GET /api/v1/bigscreen/ HTTP/1.1" 200 16287
INFO 2025-07-28 18:38:06,403 basehttp 12244 10608 "GET /api/v1/bigscreen/avatar/65/ HTTP/1.1" 200 90001
INFO 2025-07-28 18:38:06,537 basehttp 12244 11948 "GET /api/v1/bigscreen/avatar/46/ HTTP/1.1" 200 82631
INFO 2025-07-28 18:38:06,668 basehttp 12244 11820 "GET /api/v1/bigscreen/avatar/77/ HTTP/1.1" 200 353564
INFO 2025-07-28 18:38:06,788 basehttp 12244 11952 "GET /api/v1/bigscreen/avatar/58/ HTTP/1.1" 200 25299
INFO 2025-07-28 18:38:06,920 basehttp 12244 7428 "GET /api/v1/bigscreen/avatar/55/ HTTP/1.1" 200 114033
INFO 2025-07-28 18:38:07,055 basehttp 12244 7420 "GET /api/v1/bigscreen/avatar/80/ HTTP/1.1" 200 946429
INFO 2025-07-28 18:38:07,182 basehttp 12244 3976 "GET /api/v1/bigscreen/avatar/76/ HTTP/1.1" 200 859232
INFO 2025-07-28 18:38:07,371 basehttp 12244 6572 "GET /api/v1/bigscreen/avatar/49/ HTTP/1.1" 200 28902
INFO 2025-07-28 18:38:07,508 basehttp 12244 4732 "GET /api/v1/bigscreen/avatar/64/ HTTP/1.1" 200 465753
INFO 2025-07-28 18:38:07,632 basehttp 12244 488 "GET /api/v1/bigscreen/avatar/73/ HTTP/1.1" 200 469852
INFO 2025-07-28 18:38:07,761 basehttp 12244 10092 "GET /api/v1/bigscreen/avatar/62/ HTTP/1.1" 200 66907
INFO 2025-07-28 18:38:07,896 basehttp 12244 8872 "GET /api/v1/bigscreen/avatar/56/ HTTP/1.1" 200 1741892
INFO 2025-07-28 18:38:08,033 basehttp 12244 6848 "GET /api/v1/bigscreen/avatar/60/ HTTP/1.1" 200 643403
INFO 2025-07-28 18:38:08,157 basehttp 12244 10260 "GET /api/v1/bigscreen/avatar/75/ HTTP/1.1" 200 202704
INFO 2025-07-28 18:38:08,307 basehttp 12244 1936 "GET /api/v1/bigscreen/avatar/47/ HTTP/1.1" 200 532903
INFO 2025-07-28 18:38:08,439 basehttp 12244 10872 "GET /api/v1/bigscreen/avatar/71/ HTTP/1.1" 200 2059776
INFO 2025-07-28 18:38:08,566 basehttp 12244 11756 "GET /api/v1/bigscreen/avatar/63/ HTTP/1.1" 200 47116
INFO 2025-07-28 18:38:08,702 basehttp 12244 6384 "GET /api/v1/bigscreen/avatar/69/ HTTP/1.1" 200 590350
INFO 2025-07-28 18:38:08,821 basehttp 12244 784 "GET /api/v1/bigscreen/avatar/68/ HTTP/1.1" 200 64110
INFO 2025-07-28 18:38:08,951 basehttp 12244 5936 "GET /api/v1/bigscreen/avatar/52/ HTTP/1.1" 200 802203
INFO 2025-07-28 18:38:09,075 basehttp 12244 7896 "GET /api/v1/bigscreen/avatar/67/ HTTP/1.1" 200 80159
INFO 2025-07-28 18:38:09,205 basehttp 12244 1360 "GET /api/v1/bigscreen/avatar/54/ HTTP/1.1" 200 586289
INFO 2025-07-28 18:38:09,351 basehttp 12244 2640 "GET /api/v1/bigscreen/avatar/70/ HTTP/1.1" 200 1036033
INFO 2025-07-28 18:38:09,484 basehttp 12244 2172 "GET /api/v1/bigscreen/avatar/45/ HTTP/1.1" 200 79177
INFO 2025-07-28 18:38:09,615 basehttp 12244 3380 "GET /api/v1/bigscreen/avatar/59/ HTTP/1.1" 200 42446
INFO 2025-07-28 18:38:09,745 basehttp 12244 3120 "GET /api/v1/bigscreen/avatar/50/ HTTP/1.1" 200 400250
INFO 2025-07-28 18:38:09,875 basehttp 12244 2780 "GET /api/v1/bigscreen/avatar/61/ HTTP/1.1" 200 92097
INFO 2025-07-28 18:38:10,000 basehttp 12244 5508 "GET /api/v1/bigscreen/avatar/51/ HTTP/1.1" 200 1229427
INFO 2025-07-28 18:38:10,139 basehttp 12244 8368 "GET /api/v1/bigscreen/avatar/74/ HTTP/1.1" 200 483505
INFO 2025-07-28 18:38:10,276 basehttp 12244 5548 "GET /api/v1/bigscreen/avatar/78/ HTTP/1.1" 200 499083
INFO 2025-07-28 18:38:10,413 basehttp 12244 8280 "GET /api/v1/bigscreen/avatar/72/ HTTP/1.1" 200 465711
INFO 2025-07-28 18:38:10,534 basehttp 12244 1900 "GET /api/v1/bigscreen/avatar/44/ HTTP/1.1" 200 86304
INFO 2025-07-28 18:38:10,664 basehttp 12244 7464 "GET /api/v1/bigscreen/avatar/66/ HTTP/1.1" 200 240436
INFO 2025-07-28 18:38:10,793 basehttp 12244 3504 "GET /api/v1/bigscreen/avatar/36/ HTTP/1.1" 200 451964
INFO 2025-07-28 18:38:10,934 basehttp 12244 664 "GET /api/v1/bigscreen/avatar/79/ HTTP/1.1" 200 564233
INFO 2025-07-28 18:38:11,045 basehttp 12244 748 "GET /api/v1/bigscreen/avatar/48/ HTTP/1.1" 200 637803
INFO 2025-07-28 18:38:11,172 basehttp 12244 10940 "GET /api/v1/bigscreen/avatar/53/ HTTP/1.1" 200 171656
INFO 2025-07-28 18:38:11,318 basehttp 12244 10800 "GET /api/v1/bigscreen/avatar/57/ HTTP/1.1" 200 464727
INFO 2025-07-28 18:39:53,258 basehttp 12244 11248 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 18:51:05,483 basehttp 12244 9628 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 18:51:05,833 basehttp 12244 11640 "GET /api/v1/users/profile/ HTTP/1.1" 200 1137
INFO 2025-07-28 18:51:05,883 basehttp 12244 3612 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 173
INFO 2025-07-28 18:51:05,903 basehttp 12244 4476 "GET /api/v1/performance/records/stats/ HTTP/1.1" 200 1382
INFO 2025-07-28 18:51:09,612 views 12244 7120 用户 test 登出
INFO 2025-07-28 18:51:09,614 basehttp 12244 7120 "POST /api/v1/users/auth/logout/ HTTP/1.1" 200 41
INFO 2025-07-28 18:51:17,176 views 12244 10800 用户 admin 登录成功，IP: 127.0.0.1
INFO 2025-07-28 18:51:17,176 basehttp 12244 10800 "POST /api/v1/users/auth/login/ HTTP/1.1" 200 1429
INFO 2025-07-28 18:51:17,327 basehttp 12244 12220 "GET /api/v1/workplan/reminders/ HTTP/1.1" 200 138
INFO 2025-07-28 18:51:18,568 basehttp 12244 11152 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-28 18:51:18,616 basehttp 12244 10124 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
INFO 2025-07-28 18:51:21,406 basehttp 12244 9556 "GET /api/v1/users/profile/ HTTP/1.1" 200 935
INFO 2025-07-28 18:51:21,587 basehttp 12244 11560 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 200 7451
INFO 2025-07-28 18:52:13,084 basehttp 12244 4080 "GET /api/v1/users/manage/110/ HTTP/1.1" 200 1137
INFO 2025-07-28 18:56:41,051 autoreload 12244 6092 C:\code\NPC\backend\api\users\models.py changed, reloading.
INFO 2025-07-28 18:56:41,873 autoreload 8524 5320 Watching for file changes with StatReloader
INFO 2025-07-28 18:56:59,998 autoreload 8524 5320 C:\code\NPC\backend\api\users\models.py changed, reloading.
INFO 2025-07-28 18:57:00,628 autoreload 5156 10152 Watching for file changes with StatReloader
INFO 2025-07-28 18:57:53,666 autoreload 5156 10152 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-28 18:57:54,328 autoreload 7788 10148 Watching for file changes with StatReloader
INFO 2025-07-28 18:59:20,295 autoreload 7788 10148 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-28 18:59:20,929 autoreload 9436 5248 Watching for file changes with StatReloader
INFO 2025-07-28 18:59:39,060 autoreload 9436 5248 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-28 18:59:39,708 autoreload 9844 9444 Watching for file changes with StatReloader
ERROR 2025-07-28 18:59:57,183 log 9844 9996 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 18:59:57,183 log 9844 6380 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 18:59:57,183 log 9844 11884 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 18:59:57,183 log 9844 10236 Internal Server Error: /api/v1/users/manage/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 408, in get
    'results': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 18:59:57,191 basehttp 9844 9996 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 18:59:57,198 basehttp 9844 6380 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 18:59:57,217 basehttp 9844 11884 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 18:59:57,228 basehttp 9844 10236 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 500 198507
INFO 2025-07-28 18:59:57,246 basehttp 9844 3948 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
ERROR 2025-07-28 19:03:44,198 log 9844 8036 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:44,203 basehttp 9844 8036 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:03:44,439 log 9844 2648 Internal Server Error: /api/v1/users/manage/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 408, in get
    'results': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:44,481 log 9844 3540 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:44,498 basehttp 9844 2648 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 500 198507
ERROR 2025-07-28 19:03:44,507 log 9844 9800 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:44,514 basehttp 9844 3540 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:03:44,544 basehttp 9844 9800 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
INFO 2025-07-28 19:03:44,634 basehttp 9844 11564 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
ERROR 2025-07-28 19:03:58,405 log 9844 8332 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:58,417 basehttp 9844 8332 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:03:58,616 log 9844 5264 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:58,654 log 9844 3664 Internal Server Error: /api/v1/users/manage/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 408, in get
    'results': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:58,688 basehttp 9844 3664 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 500 198507
ERROR 2025-07-28 19:03:58,654 basehttp 9844 5264 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:03:58,707 log 9844 12048 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:03:58,715 basehttp 9844 12048 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
INFO 2025-07-28 19:03:58,804 basehttp 9844 11984 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
ERROR 2025-07-28 19:04:26,971 log 9844 7464 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:04:26,976 basehttp 9844 7464 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:04:27,199 log 9844 6440 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:04:27,237 log 9844 8924 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:04:27,269 basehttp 9844 8924 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:04:27,262 basehttp 9844 6440 "GET /api/v1/users/profile/ HTTP/1.1" 500 255043
ERROR 2025-07-28 19:04:27,255 log 9844 1932 Internal Server Error: /api/v1/users/manage/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 408, in get
    'results': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 797, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 715, in to_representation
    return [
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 384, in __iter__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:04:27,301 basehttp 9844 1932 "GET /api/v1/users/manage/?page=1&page_size=20 HTTP/1.1" 500 198507
INFO 2025-07-28 19:04:27,370 basehttp 9844 3152 "GET /api/v1/opinions/statistics/ HTTP/1.1" 200 656
ERROR 2025-07-28 19:04:42,919 log 9844 7804 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:04:42,931 basehttp 9844 7804 "GET /api/v1/users/profile/ HTTP/1.1" 500 255025
ERROR 2025-07-28 19:05:00,749 log 9844 9864 Internal Server Error: /api/v1/users/profile/
Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 516, in __get__
    rel_obj = self.related.get_cached_value(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\mixins.py", line 37, in get_cached_value
    return instance._state.fields_cache[self.cache_name]
KeyError: 'representative'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
sqlite3.OperationalError: no such column: representatives.levels

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\code\NPC\backend\api\users\views.py", line 204, in get
    'data': serializer.data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 573, in data
    ret = super().data
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 251, in data
    self._data = self.to_representation(self.instance)
  File "C:\code\NPC\backend\api\users\serializers.py", line 407, in to_representation
    data = super().to_representation(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\serializers.py", line 527, in to_representation
    attribute = field.get_attribute(instance)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 437, in get_attribute
    return get_attribute(instance, self.source_attrs)
  File "C:\code\NPC\backend\.venv\lib\site-packages\rest_framework\fields.py", line 104, in get_attribute
    instance = getattr(instance, attr)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\fields\related_descriptors.py", line 523, in __get__
    rel_obj = self.get_queryset(instance=instance).get(**filter_args)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 629, in get
    num = len(clone)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 366, in __len__
    self._fetch_all()
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 1949, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\models\sql\compiler.py", line 1623, in execute_sql
    cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 100, in _execute
    with self.db.wrap_database_errors:
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
  File "C:\code\NPC\backend\.venv\lib\site-packages\django\db\backends\sqlite3\base.py", line 360, in execute
    return super().execute(query, params)
django.db.utils.OperationalError: no such column: representatives.levels
ERROR 2025-07-28 19:05:00,752 basehttp 9844 9864 "GET /api/v1/users/profile/ HTTP/1.1" 500 255025
INFO 2025-07-28 19:06:30,306 autoreload 9844 9444 C:\code\NPC\backend\api\bigscreen\services.py changed, reloading.
INFO 2025-07-28 19:06:30,942 autoreload 10672 11876 Watching for file changes with StatReloader
INFO 2025-07-28 19:06:57,150 autoreload 10672 11876 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-28 19:06:57,744 autoreload 11972 4208 Watching for file changes with StatReloader
INFO 2025-07-28 19:07:18,861 autoreload 11972 4208 C:\code\NPC\backend\api\users\serializers.py changed, reloading.
INFO 2025-07-28 19:07:19,436 autoreload 7740 9848 Watching for file changes with StatReloader
