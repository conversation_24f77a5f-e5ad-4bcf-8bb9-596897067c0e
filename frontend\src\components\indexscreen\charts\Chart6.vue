<template>
  <div style="position: relative; width: 100%; height: 95%">
    <!-- 右上角信息栏 -->
    <div
      style="
        position: absolute;
        right: 20px;
        top: 10px;
        z-index: 10;
        display: flex;
        align-items: center;
      "
    >
      <span
        style="
          color: #fff;
          font-size: 14px;
          margin-right: 10px;
          border-bottom: 1px skyblue solid;
        "
      >
        当月累计履职
        <span style="color: #ffd700; font-size: 20px">{{ total }}</span> 条
      </span>
      <select
        v-model="selectedMonth"
        @change="onMonthChange"
        style="
          background: #0a2740;
          color: #ffd700;
          border: 1px solid #1e90ff;
          border-radius: 4px;
          padding: 2px 8px;
        "
      >
        <option v-for="month in months" :key="month" :value="month">
          {{ month }}
        </option>
      </select>
    </div>
    <!-- 图表 -->
    <div ref="myChart" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
export default {
  props: {
    months: {
      type: Array,
      default: () => [],
    },
    chartData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      total: 0,
      myChart: null,
      selectedMonth: '2025年7月',
    }
  },
  mounted() {
    // 使用nextTick确保DOM已经渲染
    this.$nextTick(() => {
      // 如果有月份数据，使用第一个月份作为默认选中
      if (this.months && this.months.length > 0) {
        this.selectedMonth = this.months[0]
      }
      this.initChart()
    })
  },
  watch: {
    months: {
      handler(newMonths) {
        this.$nextTick(() => {
          if (newMonths && newMonths.length > 0) {
            this.selectedMonth = newMonths[0]
          }
          this.initChart()
        })
      },
      immediate: true,
    },
    chartData: {
      handler() {
        this.initChart()
      },
      deep: true,
    },
    selectedMonth() {
      this.initChart()
    },
  },
  methods: {
    initChart() {
      // 检查DOM元素是否存在
      if (!this.$refs.myChart) {
        console.warn('Chart6: DOM element not ready')
        return
      }

      // 检查数据是否存在
      if (!this.chartData || !this.selectedMonth) {
        console.warn('Chart6: Data not ready')
        return
      }

      if (!this.myChart) {
        this.myChart = this.$echarts.init(this.$refs.myChart)
      }
      const { categories, values } = this.chartData[this.selectedMonth] || {
        categories: [],
        values: [],
      }
      this.total = values.reduce((a, b) => a + b, 0)
      const option = {
        tooltip: { trigger: 'axis' },
        grid: { left: 40, right: 30, bottom: 5, top: 70, containLabel: true },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', rotate: 45, fontSize: 10 },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#ffffff', fontSize: 12 }, // 使用白色字体
          splitLine: { lineStyle: { color: '#0c4787', type: 'dashed' } },
          axisLine: { show: 'true' },
        },
        series: [
          {
            data: values,
            type: 'bar',
            barWidth: 15,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: '#69b2ff' },
                  { offset: 1, color: '#205afd' },
                ],
              },
            },
          },
        ],
      }
      this.myChart.setOption(option)
    },
    onMonthChange() {
      this.initChart()
    },
  },
}
</script>

<style scoped></style>
