<template>
  <div class="representative-layout">
    <!-- 头部 -->
    <el-header class="layout-header">
      <div class="header-left">
        <el-icon size="24" style="color: var(--china-red); margin-right: 10px;">
          <User />
        </el-icon>
        <span class="system-title">人大代表履职服务与管理平台</span>
      </div>
      <div class="header-right">
        <!-- 通知图标 -->
        <NotificationIcon />
        
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar
              :size="32"
              :src="userAvatar"
              style="background-color: var(--china-red);"
            >
              {{ userStore.userName.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ userStore.userName }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人信息管理
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体布局 -->
    <el-container class="main-container">
      <!-- 侧边栏 -->
      <el-aside class="layout-aside">
        <el-menu
          :default-active="$route.path"
          router
          class="nav-menu"
        >
          <el-menu-item index="/representative">
            <el-icon><House /></el-icon>
            <span>工作台概览</span>
          </el-menu-item>
          <el-menu-item index="/representative/profile">
            <el-icon><User /></el-icon>
            <span>个人信息管理</span>
          </el-menu-item>
          <el-menu-item index="/representative/records">
            <el-icon><Document /></el-icon>
            <span>履职记录管理</span>
          </el-menu-item>
          <el-menu-item index="/representative/annual-achievements">
            <el-icon><Trophy /></el-icon>
            <span>年度履职AI分析展示</span>
          </el-menu-item>
          <el-menu-item index="/representative/opinions">
            <el-icon><ChatLineRound /></el-icon>
                          <span>意见建议</span>
          </el-menu-item>
          <el-menu-item index="/representative/knowledge-qa">
            <el-icon><DocumentCopy /></el-icon>
            <span>法律政策互动AI问答</span>
          </el-menu-item>
          <el-menu-item index="/representative/password-change">
            <el-icon><Key /></el-icon>
            <span>账号密码修改</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 内容区域 -->
      <el-main class="layout-main">
        <!-- 路由出口：如果是根路径显示概览，否则显示子页面 -->
        <div v-if="$route.path === '/representative'" class="dashboard-container">
          <!-- 欢迎卡片 -->
          <div class="welcome-card">
            <h2>欢迎回来，{{ representativeName }} 代表</h2>
            <p>{{ currentDate }} | {{ userStore.roleText }}</p>
            <div class="role-info">
              <div class="info-item">
                <el-icon class="info-icon"><User /></el-icon>
                <div class="info-content">
                  <span class="label">姓名</span>
                  <span class="value">{{ representativeName }}</span>
                </div>
              </div>
              <div class="info-item">
                <el-icon class="info-icon"><Medal /></el-icon>
                <div class="info-content">
                  <span class="label">代表层级</span>
                  <span class="value">{{ representativeLevel }}</span>
                </div>
              </div>
              <div class="info-item">
                <el-icon class="info-icon"><UserFilled /></el-icon>
                <div class="info-content">
                  <span class="label">民族</span>
                  <span class="value">{{ representativeNationality }}</span>
                </div>
              </div>
              <div class="info-item">
                <el-icon class="info-icon"><Star /></el-icon>
                <div class="info-content">
                  <span class="label">党派</span>
                  <span class="value">{{ representativeParty }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计数据 -->
          <div class="stats-section">
            <div class="stats-container">
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.totalRecords }}</div>
                    <div class="stat-label">履职记录</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #409eff;"><Document /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.thisMonthRecords }}</div>
                    <div class="stat-label">本月履职</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #67c23a;"><Calendar /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.unfinishedOpinions }}</div>
                    <div class="stat-label">未完成意见</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #e6a23c;"><Clock /></el-icon>
                </el-card>
              </div>
              <div class="stat-item">
                <el-card class="stat-card" v-loading="loadingStats">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.completedOpinions }}</div>
                    <div class="stat-label">已完成意见</div>
                  </div>
                  <el-icon class="stat-icon" style="color: #67c23a;"><CircleCheck /></el-icon>
                </el-card>
              </div>
            </div>
          </div>

          <!-- 最近活动 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="12">
              <el-card header="部分最近履职记录" v-loading="loadingRecentRecords">
                <el-empty v-if="!recentRecords.length && !loadingRecentRecords" description="暂无履职记录" />
                <el-timeline v-else>
                  <el-timeline-item
                    v-for="record in recentRecords"
                    :key="record.id"
                    :timestamp="record.date"
                  >
                    {{ record.title }}
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card header="部分最近更新意见" v-loading="loadingRecentOpinions">
                <el-empty v-if="!recentOpinions.length && !loadingRecentOpinions" description="暂无最近更新意见" />
                <el-timeline v-else>
                  <el-timeline-item
                    v-for="opinion in recentOpinions"
                    :key="opinion.id"
                    :timestamp="opinion.date"
                    :type="getOpinionTimelineType(opinion.status)"
                  >
                    <div class="activity-content">
                      <div class="opinion-title" :title="opinion.title">
                        {{ truncateTitle(opinion.title, 25) }}
                      </div>
                      <div class="opinion-meta">
                        <el-tag size="small" :type="getOpinionTagType(opinion.status)">
                          {{ opinion.statusText }}
                        </el-tag>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <!-- 子路由内容 -->
        <router-view v-else />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import NotificationIcon from '@/components/NotificationIcon.vue'
import { performanceAPI } from '@/api/modules/performance/api'
import { opinionAPI, opinionUtils } from '@/api/modules/opinion/api'
import {
  User,
  ArrowDown,
  SwitchButton,
  House,
  Document,
  ChatLineRound,
  Trophy,
  DocumentCopy,
  Key,
  Medal,
  UserFilled,
  Star,
  Calendar,
  Clock,
  CircleCheck
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 代表信息计算属性
const representativeInfo = computed(() => userStore.userInfo.representative_info || {})
const representativeName = computed(() => representativeInfo.value.name || userStore.userName)
const representativeLevel = computed(() => representativeInfo.value.level || '未填写')
const representativeNationality = computed(() => representativeInfo.value.nationality || '未填写')
const representativeParty = computed(() => representativeInfo.value.party || '未填写')

// 用户头像计算属性
const userAvatar = computed(() => {
  return representativeInfo.value.avatar || null
})

// 当前日期
const currentDate = computed(() => {
  const now = new Date()
  return now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 加载状态
const loadingStats = ref(false)
const loadingRecentRecords = ref(false)
const loadingRecentOpinions = ref(false)

// 统计数据
const stats = ref({
  totalRecords: 0,
  thisMonthRecords: 0,
  unfinishedOpinions: 0, // 除了已办结之外的所有意见数量
  completedOpinions: 0   // 已办结意见数量
})

// 最近履职记录（限制5条）
const recentRecords = ref([])

// 最近更新意见列表
const recentOpinions = ref([])

// 辅助方法
const truncateTitle = (title, maxLength) => {
  if (title.length <= maxLength) return title
  return title.substring(0, maxLength) + '...'
}

// 格式化日期时间为精确到分钟的格式
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''
  const date = new Date(dateTimeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getOpinionTagType = (status) => {
  const typeMap = {
    submitted: 'warning',    // 已提交 - 警告色
    approved: 'success',     // 已通过待转交 - 成功色
    transferred: 'info',     // 已转交 - 信息色
    in_progress: 'danger',   // 处理中 - 危险色
    completed: 'success',    // 已办结 - 成功色
    rejected: 'danger'       // 已驳回 - 危险色
  }
  return typeMap[status] || 'info'
}

const getOpinionTimelineType = (status) => {
  const typeMap = {
    submitted: 'warning',    // 已提交 - 警告色
    approved: 'success',     // 已通过待转交 - 成功色
    transferred: 'primary',  // 已转交 - 主要色
    in_progress: 'danger',   // 处理中 - 危险色
    completed: 'success',    // 已办结 - 成功色
    rejected: 'danger'       // 已驳回 - 危险色
  }
  return typeMap[status] || 'info'
}

// 数据获取方法
const fetchStatistics = async () => {
  try {
    loadingStats.value = true
    loadingRecentRecords.value = true
    loadingRecentOpinions.value = true
    
    // 并行获取履职统计和意见统计
    const [performanceStats, opinionStats] = await Promise.all([
      performanceAPI.getRecordStats(),
      opinionAPI.getOpinionStatistics()
    ])
    
    // 处理履职统计数据
    const performanceData = performanceStats.data || {}
    stats.value.totalRecords = performanceData.total_count || 0
    stats.value.thisMonthRecords = performanceData.monthly_count || 0
    
    // 从统计API获取最近履职记录（不再使用分页请求）
    recentRecords.value = (performanceData.recent_records || []).map(record => ({
      id: record.id,
      title: record.performance_content,
      date: formatDateTime(record.performance_date)
    }))
    
    // 处理意见统计数据
    let opinionData = {}
    if (opinionStats.data.success) {
      opinionData = opinionStats.data.data || {}
    } else {
      opinionData = opinionStats.data || {}
    }
    
    // 统计数据
    stats.value.unfinishedOpinions = opinionData.pending_opinions || 0
    stats.value.completedOpinions = opinionData.completed_opinions || 0
    
    // 从统计API获取最近更新意见（不再使用分页请求）
    recentOpinions.value = (opinionData.recent_opinions || []).map(opinion => ({
      id: opinion.id,
      title: opinion.title,
      status: opinion.current_status,
      statusText: opinionUtils.getStatusLabel(opinion.current_status),
      date: opinionUtils.formatDateTime(opinion.last_updated_time)
    }))
    
    console.log('代表工作台数据:', {
      stats: stats.value,
      recentRecords: recentRecords.value,
      recentOpinions: recentOpinions.value
    })
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loadingStats.value = false
    loadingRecentRecords.value = false
    loadingRecentOpinions.value = false
  }
}

// 注意：最近记录和最近意见现在直接从统计API获取，不再需要单独的分页请求

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/representative/profile')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 等待登出完成后再跳转
    await userStore.logout()
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  // 加载用户完整信息
  try {
    await userStore.fetchUserInfo()
  } catch (error) {
    console.warn('获取用户信息失败:', error)
  }
  
  // 一次性加载所有数据（统计数据、最近记录、最近意见）
  await fetchStatistics()
})
</script>

<style scoped>
.representative-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.system-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--china-red);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: var(--bg-color);
}

.user-name {
  font-weight: 500;
  color: var(--text-color);
}

.main-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.layout-aside {
  width: 200px;
  background: white;
  border-right: 1px solid var(--border-color);
}

.nav-menu {
  border-right: none;
  height: 100%;
}

.layout-main {
  background: var(--bg-color);
  padding: 0;
  overflow-y: auto;
}

.dashboard-container {
  padding: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, var(--china-red) 0%, #d32f2f 100%);
  color: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.3);
}

.welcome-card h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-card p {
  margin: 0 0 16px 0;
  opacity: 0.9;
  font-size: 14px;
}

.role-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 180px;
}

.info-icon {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  flex-shrink: 0;
}

.info-content {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  white-space: nowrap;
}

.value {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  flex: 1;
  min-width: 200px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.15);
}

.stat-card .el-card__body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.stat-content {
  text-align: center;
  margin-bottom: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--china-red);
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  white-space: nowrap;
}

.stat-icon {
  font-size: 28px;
  opacity: 0.8;
}

.activity-content {
  font-size: 13px;
  color: var(--text-color);
  line-height: 1.4;
}

/* 响应式调整 */
@media (min-width: 1200px) {
  .stat-item {
    min-width: 220px;
  }
  
  .stat-card .el-card__body {
    padding: 20px;
  }
  
  .stat-content {
    margin-bottom: 12px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .stat-label {
    font-size: 14px;
    margin-top: 5px;
  }
  
  .stat-icon {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .stat-item {
    min-width: 180px;
  }
}

@media (max-width: 768px) {
  .stat-card .el-card__body {
    padding: 12px;
  }
  
  .stat-content {
    margin-bottom: 6px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
}

@media (max-width: 576px) {
  .stats-container {
    gap: 8px;
  }
  
  .stat-card .el-card__body {
    padding: 10px;
  }
  
  .stat-content {
    margin-bottom: 4px;
  }
}

.opinion-item {
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.opinion-item:last-child {
  border-bottom: none;
}

.opinion-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.opinion-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.opinion-date {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-aside {
    width: 160px;
  }
  
  .system-title {
    display: none;
  }
  
  .stats-section {
    margin: 10px 0;
  }
  
  .role-info {
    flex-direction: column;
    gap: 12px;
  }
  
  .welcome-card {
    padding: 20px;
  }
  
  .welcome-card h2 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 10px;
  }
  
  .layout-aside {
    width: 120px;
  }
  
  .user-name {
    display: none;
  }
  
  .welcome-card {
    padding: 15px;
    margin-bottom: 15px;
  }
  
  .welcome-card h2 {
    font-size: 18px;
  }
  
  .info-item {
    min-width: auto;
  }
  
  .dashboard-container {
    padding: 10px;
  }
}
</style> 