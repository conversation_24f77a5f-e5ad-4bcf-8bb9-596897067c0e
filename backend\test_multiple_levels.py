#!/usr/bin/env python
"""
测试多选层级功能的脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from api.users.models import Representative, User


def test_multiple_levels():
    """测试多选层级功能"""
    print("🧪 开始测试多选层级功能...")
    
    # 1. 测试现有数据的兼容性
    print("\n1. 测试现有数据兼容性:")
    representatives = Representative.objects.all()[:5]
    for rep in representatives:
        print(f"  代表: {rep.name}")
        print(f"    原层级(level): {rep.level}")
        print(f"    新层级(levels): {rep.levels}")
        print(f"    显示文本: {rep.get_levels_display()}")
        print()
    
    # 2. 测试设置多个层级
    print("2. 测试设置多个层级:")
    if representatives:
        test_rep = representatives[0]
        print(f"  测试代表: {test_rep.name}")
        
        # 设置多个层级
        new_levels = ['县区人大代表', '市人大代表']
        test_rep.set_levels(new_levels)
        test_rep.save()
        
        print(f"  设置层级: {new_levels}")
        print(f"  保存后层级: {test_rep.levels}")
        print(f"  显示文本: {test_rep.get_levels_display()}")
        print()
    
    # 3. 测试层级操作方法
    print("3. 测试层级操作方法:")
    if representatives:
        test_rep = representatives[0]
        print(f"  测试代表: {test_rep.name}")
        
        # 添加层级
        test_rep.add_level('自治区人大代表')
        print(f"  添加'自治区人大代表'后: {test_rep.levels}")
        
        # 检查是否包含层级
        has_county = test_rep.has_level('县区人大代表')
        has_national = test_rep.has_level('全国人大代表')
        print(f"  包含'县区人大代表': {has_county}")
        print(f"  包含'全国人大代表': {has_national}")
        
        # 移除层级
        test_rep.remove_level('县区人大代表')
        print(f"  移除'县区人大代表'后: {test_rep.levels}")
        
        test_rep.save()
        print()
    
    # 4. 测试大屏统计
    print("4. 测试大屏统计功能:")
    from api.bigscreen.services import BigScreenDataService
    chart1_data = BigScreenDataService.get_chart1_data()
    print(f"  层级统计数据: {chart1_data}")
    print()
    
    print("✅ 多选层级功能测试完成!")


if __name__ == '__main__':
    test_multiple_levels()
