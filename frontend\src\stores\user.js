import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authAPI, STORAGE_KEYS } from '@/api'

export const useUserStore = defineStore('user', () => {
  // 状态 - 使用ref管理响应式数据
  const token = ref(localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN) || '')
  const userInfo = ref(JSON.parse(localStorage.getItem(STORAGE_KEYS.USER_INFO) || '{}'))
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value.id)
  const isRepresentative = computed(() => userInfo.value.role === 'representative')
  const isStaff = computed(() => userInfo.value.role === 'staff')
  const userName = computed(() => {
    // 根据用户角色获取正确的姓名
    if (userInfo.value.role === 'representative' && userInfo.value.representative_info) {
      return userInfo.value.representative_info.name || userInfo.value.username || '代表'
    } else if (userInfo.value.role === 'staff' && userInfo.value.staff_info) {
      return userInfo.value.staff_info.name || userInfo.value.username || '工作人员'
    }
    return userInfo.value.name || userInfo.value.username || '用户'
  })
  const roleText = computed(() => {
    const roleMap = {
      'representative': '人大代表',
      'staff': '站点工作人员'
    }
    return roleMap[userInfo.value.role] || '未知角色'
  })
  
  /**
   * 登录方法
   * @param {Object} loginForm - 登录表单数据
   * @returns {Promise} 登录结果
   */
  const loginUser = async (loginForm) => {
    try {
      const response = await authAPI.login(loginForm)
      
      // 响应拦截器返回的是完整的axios响应对象，需要访问response.data
      const responseData = response.data
      
      if (responseData.success) {
        // 保存tokens到本地存储
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, responseData.data.access_token)
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, responseData.data.refresh_token)

        // 保存用户信息和token
        token.value = responseData.data.access_token
        userInfo.value = responseData.data.user
        
        // 持久化存储用户信息
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
        
        ElMessage.success('登录成功')
        return { success: true, userInfo: userInfo.value }
      } else {
        ElMessage.error(responseData.message || '登录失败')
        return { success: false, message: responseData.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      
      // 处理不同类型的错误
      if (error.response?.data) {
        const errorMsg = error.response.data.message || '登录失败，请检查用户名和密码'
        ElMessage.error(errorMsg)
        return { success: false, message: errorMsg }
      }
      
      ElMessage.error('网络连接失败，请检查网络或后端服务是否启动')
      return { success: false, message: '网络连接失败' }
    }
  }
  
  /**
   * 登出方法
   */
  const logoutUser = async () => {
    try {
      // 尝试调用登出API（如果可能的话）
      const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
      if (refreshToken && token.value) {
        try {
          await authAPI.logout({ refresh_token: refreshToken })
          console.log('✅ 服务端登出成功')
        } catch (apiError) {
          // 登出API失败不影响客户端状态清理
          console.warn('⚠️ 服务端登出失败，但将继续清理客户端状态:', apiError.message)
          
          // 如果是401错误，说明token已经失效，这是正常情况
          if (apiError.response?.status === 401) {
            console.log('Token已失效，无需服务端登出')
          }
        }
      }
    } catch (error) {
      console.warn('登出过程出现异常:', error)
    } finally {
      // 无论API调用是否成功，都要清除本地状态
      console.log('🧹 清理本地认证状态')
      
      // 清理响应式状态
      token.value = ''
      userInfo.value = {}
      
      // 清除本地存储
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
      localStorage.removeItem(STORAGE_KEYS.USER_INFO)
      
      console.log('✅ 本地状态清理完成')
    }
  }
  
  /**
   * 获取用户信息
   */
  const fetchUserInfo = async () => {
    try {
      const response = await authAPI.getUserProfile()
      const responseData = response.data
      
      if (responseData.success) {
        userInfo.value = responseData.data
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
        return { success: true, data: userInfo.value }
      } else {
        return { success: false, message: responseData.message }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { success: false, message: '获取用户信息失败' }
    }
  }
  
  /**
   * 更新用户信息
   * @param {Object} newUserInfo - 新的用户信息
   */
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
  }

  /**
   * 更新用户个人资料
   * @param {Object} profileData - 个人资料数据
   */
  const updateProfile = async (profileData) => {
    try {
      const response = await authAPI.updateProfile(profileData)
      const responseData = response.data
      
      if (responseData.success) {
        // 更新本地用户信息
        userInfo.value = responseData.data
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
        return { success: true, data: responseData.data }
      } else {
        return { success: false, message: responseData.message }
      }
    } catch (error) {
      console.error('更新个人资料失败:', error)
      const errorMsg = error.response?.data?.message || '更新失败，请重试'
      return { success: false, message: errorMsg }
    }
  }
  
  /**
   * 检查权限
   * @param {string} requiredRole - 需要的角色
   * @returns {boolean} 是否有权限
   */
  const hasPermission = (requiredRole) => {
    if (!requiredRole) return true
    return userInfo.value.role === requiredRole
  }
  
  /**
   * 初始化用户状态（应用启动时调用）
   */
  const initializeAuth = () => {
    // 检查本地存储中的token
    const storedToken = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
    const storedUserInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO)
    
    if (storedToken && storedUserInfo) {
      try {
        token.value = storedToken
        userInfo.value = JSON.parse(storedUserInfo)
        
        console.log('用户认证状态已恢复:', {
          username: userInfo.value.username,
          role: userInfo.value.role
        })
        
        // 验证token有效性
        validateTokenAsync()
      } catch (error) {
        console.error('恢复用户状态失败:', error)
        // 清除损坏的数据
        clearAuthState()
      }
    }
  }
  
  /**
   * 清理认证状态（内部方法）
   */
  const clearAuthState = () => {
    token.value = ''
    userInfo.value = {}
    localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
    localStorage.removeItem(STORAGE_KEYS.USER_INFO)
  }
  
  /**
   * 异步验证token有效性
   */
  const validateTokenAsync = async () => {
    try {
      // 尝试获取用户信息来验证token
      const response = await authAPI.getUserProfile()
      if (response.data.success) {
        // Token有效，更新用户信息
        userInfo.value = response.data.data
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo.value))
        console.log('🔒 Token验证成功，用户信息已更新')
      }
    } catch (error) {
      console.warn('⚠️ Token验证失败:', error.message)
      // 如果验证失败，让拦截器处理token刷新或清理状态
      // 这里不主动清理状态，避免与拦截器冲突
    }
  }
  
  /**
   * 检查认证状态是否有效
   * @returns {boolean} 认证状态是否有效
   */
  const isAuthValid = () => {
    const hasToken = !!token.value
    const hasUserInfo = !!userInfo.value.id
    const hasStoredToken = !!localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
    
    return hasToken && hasUserInfo && hasStoredToken
  }
  
  return {
    // 状态
    token,
    userInfo,
    
    // 计算属性
    isLoggedIn,
    isRepresentative,
    isStaff,
    userName,
    roleText,
    
    // 方法
    login: loginUser,
    logout: logoutUser,
    fetchUserInfo,
    updateUserInfo,
    updateProfile,
    hasPermission,
    initializeAuth,
    clearAuthState,
    validateTokenAsync,
    isAuthValid
  }
}) 